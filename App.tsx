import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { GoogleGenAI, Modality, Type, FunctionDeclaration, LiveServerMessage } from '@google/genai';
import { collection, addDoc, serverTimestamp, getDocs, query, orderBy, onSnapshot, doc, updateDoc, where, deleteDoc, limit, writeBatch } from 'firebase/firestore';
import { db } from './firebase/config';

import { ConversationTurn, EmailContext, EventType, HACCPEvent, Location, Vendor, Species, PurchaseOrder, UpdateEventField } from './types';
import { ConversationLog } from './components/ConversationLog';
import { StatusIndicator } from './components/StatusIndicator';
import { MicrophoneIcon, StopIcon, CalendarIcon, ClockIcon, XIcon, PhotoIcon, SparklesIcon, CameraIcon, DocumentArrowUpIcon, HamburgerIcon } from './components/IconComponents';
import { Dashboard } from './components/Dashboard';
import { Sidebar, SidebarView } from './components/Sidebar';
import { createBlob, decode, decodeAudioData } from './utils/audioUtils';
import { blobToBase64, uploadImage } from './utils/imageUtils';
// Inventory management dashboard
import InventoryManagement from './components/InventoryManagement';
import { ReportsView } from './components/ReportsView';
import { SettingsView } from './components/SettingsView';
import { HaccpLogsView } from './components/HaccpLogsView';
import { CalendarView } from './components/CalendarView';
import { VendorsView } from './components/VendorsView';
import { SpeciesView } from './components/SpeciesView';
import { LocationsView } from './components/LocationsView';
import { OrdersView } from './components/OrdersView';
import { EmailDraftModal } from './components/EmailDraftModal';
import { CameraModal } from './components/CameraModal';
import { ImportModal } from './components/ImportModal';
import { docToPlainObject } from './utils/firestoreUtils';
import { TemperatureView } from './components/TemperatureView';
import { LotTrackingView } from './components/LotTracking/LotTrackingView';
import { Login } from './components/Login';
import { auth } from './firebase/config';
import { onAuthStateChanged, signOut, User } from 'firebase/auth';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Link from '@mui/material/Link';
import Paper from '@mui/material/Paper';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import Tooltip from '@mui/material/Tooltip';
import Fab from '@mui/material/Fab';
import Grid from '@mui/material/GridLegacy';
import IconButton from '@mui/material/IconButton';
import { alpha } from '@mui/material/styles';
import AppThemeProvider, { TopbarActions } from './src/AppThemeProvider';

// Polyfill for webkitAudioContext
const AudioContext = window.AudioContext || (window as any).webkitAudioContext;

type ViewType = 'form' | 'dashboard' | 'calendar' | 'vendors' | 'species' | 'locations' | 'orders' | 'temperature' | 'inventory' | 'haccp' | 'reports' | 'settings' | 'lot-tracking';

const VIEW_LABELS: Record<ViewType, string> = {
    form: 'Event Form',
    dashboard: 'Dashboard',
    calendar: 'Calendar',
    vendors: 'Vendors',
    species: 'Species',
    locations: 'Locations',
    orders: 'Orders',
    temperature: 'Temperature',
    inventory: 'Inventory',
    haccp: 'HACCP Logs',
    reports: 'Reports',
    settings: 'Settings',
    'lot-tracking': 'Lot Tracking',
};

const generateTurnId = (): string => {
    const globalCrypto = typeof globalThis !== 'undefined' ? (globalThis.crypto ?? (globalThis as any).msCrypto) : undefined;
    if (globalCrypto && typeof globalCrypto.randomUUID === 'function') {
        return globalCrypto.randomUUID();
    }
    return `${Date.now()}-${Math.random().toString(36).slice(2, 10)}`;
};

const createConversationTurn = (speaker: ConversationTurn['speaker'], text: string): ConversationTurn => ({
    id: generateTurnId(),
    speaker,
    text,
});

const App: React.FC = () => {
    // App State
    const [apiKeySelected, setApiKeySelected] = useState(false);
    const [user, setUser] = useState<User | null>(null);
    const [authLoading, setAuthLoading] = useState(true);
    const [currentUser, setCurrentUser] = useState('Default User');
    const [view, setView] = useState<ViewType>('dashboard');
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(
        () => (typeof window !== 'undefined' ? window.innerWidth < 900 : true), // default closed on mobile (md breakpoint)
    );
    const [locations, setLocations] = useState<Location[]>([]);
    const [vendors, setVendors] = useState<Vendor[]>([]);
    const [species, setSpecies] = useState<Species[]>([]);
    const [events, setEvents] = useState<HACCPEvent[]>([]);
    const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
    
    // Form & Editing State
    const [editingEventId, setEditingEventId] = useState<string | null>(null);
    const [eventType, setEventType] = useState<EventType>('receiving');
    const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
    const [time, setTime] = useState(new Date().toTimeString().substring(0, 5));
    const [batchNumber, setBatchNumber] = useState('');
    const [product, setProduct] = useState('');
    const [productForm, setProductForm] = useState('');
    const [quantity, setQuantity] = useState('');
    const [temperature, setTemperature] = useState('32.0');
    const [unitPrice, setUnitPrice] = useState('');
    const [origin, setOrigin] = useState('');
    const [supplier, setSupplier] = useState('');
    const [location, setLocation] = useState('');
    const [fromLocation, setFromLocation] = useState('');
    const [notes, setNotes] = useState('');
    const [areaCleaned, setAreaCleaned] = useState('');
    const [sanitizerUsed, setSanitizerUsed] = useState('');
    const [thermometerId, setThermometerId] = useState('');
    const [calibrationMethod, setCalibrationMethod] = useState('Ice Point');
    const [result, setResult] = useState<'pass' | 'fail'>('pass');
    const [correctiveAction, setCorrectiveAction] = useState('');
    const [employeeName, setEmployeeName] = useState('');
    const [trainingTopic, setTrainingTopic] = useState('');
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [imageDescription, setImageDescription] = useState('');

    // Voice Chat State
    const [isVoicePanelOpen, setIsVoicePanelOpen] = useState(false);
    const [conversation, setConversation] = useState<ConversationTurn[]>([]);
    const [isSessionActive, setIsSessionActive] = useState<boolean>(false);
    const [status, setStatus] = useState<string>('Idle. Press Start to begin.');

    // Modal State
    const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
    const [emailContext, setEmailContext] = useState<EmailContext | null>(null);
    const [isCameraModalOpen, setIsCameraModalOpen] = useState(false);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);

    const inventory = useMemo<Record<string, number>>(() => {
        const inventoryMap: { [product: string]: number } = {};
    
        [...events].sort((a, b) => {
            const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
            const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
            return timeA - timeB;
        }).forEach(event => {
          if (!event.product || typeof event.quantity !== 'number') {
              return;
          }
    
          if (!inventoryMap[event.product]) {
            inventoryMap[event.product] = 0;
          }
    
          switch (event.eventType) {
            case 'receiving':
              inventoryMap[event.product] += event.quantity;
              break;
            case 'sales':
            case 'disposal':
              inventoryMap[event.product] -= event.quantity;
              break;
          }
        });
    
        Object.keys(inventoryMap).forEach(product => {
            if (inventoryMap[product] <= 0.01) {
                delete inventoryMap[product];
            }
        });
    
        return inventoryMap;
    }, [events]);

    const inventoryByLocation = useMemo<Record<string, Record<string, number>>>(() => {
        const inventoryMap: { [locationName: string]: { [product: string]: number } } = {};

        [...events].sort((a, b) => {
            const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
            const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
            return timeA - timeB;
        }).forEach(event => {
            if (!event.product || typeof event.quantity !== 'number') return;
            const prod = event.product;
            const qty = event.quantity;
            
            switch (event.eventType) {
                case 'receiving':
                    if(event.location) {
                        if (!inventoryMap[event.location]) inventoryMap[event.location] = {};
                        if (!inventoryMap[event.location][prod]) inventoryMap[event.location][prod] = 0;
                        inventoryMap[event.location][prod] += qty;
                    }
                    break;
                case 'sales':
                case 'disposal':
                    if (event.location && inventoryMap[event.location]?.[prod]) {
                        inventoryMap[event.location][prod] -= qty;
                    }
                    break;
                case 'relocation':
                    if (event.fromLocation && inventoryMap[event.fromLocation]?.[prod]) {
                        inventoryMap[event.fromLocation][prod] -= qty;
                    }
                    if (event.location) { // 'location' is the 'to' location
                        if (!inventoryMap[event.location]) inventoryMap[event.location] = {};
                        if (!inventoryMap[event.location][prod]) inventoryMap[event.location][prod] = 0;
                        inventoryMap[event.location][prod] += qty;
                    }
                    break;
            }
        });
        
        // Clean up zero or negative quantities
        Object.keys(inventoryMap).forEach(loc => {
            Object.keys(inventoryMap[loc]).forEach(prod => {
                if (inventoryMap[loc][prod] <= 0.01) {
                    delete inventoryMap[loc][prod];
                }
            });
            if (Object.keys(inventoryMap[loc]).length === 0) {
                delete inventoryMap[loc];
            }
        });

        return inventoryMap;
    }, [events]);


    // Refs
    const sessionPromiseRef = useRef<Promise<any> | null>(null);
    const inputAudioContextRef = useRef<AudioContext | null>(null);
    const outputAudioContextRef = useRef<AudioContext | null>(null);
    const streamRef = useRef<MediaStream | null>(null);
    const workletNodeRef = useRef<AudioWorkletNode | null>(null);
    const mediaStreamSourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
    const nextStartTimeRef = useRef<number>(0);
    const sourcesRef = useRef<Set<AudioBufferSourceNode>>(new Set());
    const currentInputTranscriptionRef = useRef('');
    const currentOutputTranscriptionRef = useRef('');
    
    // Auth state listener
    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, (user) => {
            setUser(user);
            if (user) {
                setCurrentUser(user.displayName || user.email || 'User');
            }
            setAuthLoading(false);
        });
        return () => unsubscribe();
    }, []);

    useEffect(() => {
        const checkApiKey = async () => {
            if ((window as any).aistudio) {
                const hasKey = await (window as any).aistudio.hasSelectedApiKey();
                setApiKeySelected(hasKey);
            } else {
                setApiKeySelected(true);
            }
        };
        checkApiKey();
    }, []);

    useEffect(() => {
        const qLocations = query(collection(db, "locations"), orderBy("name"));
        const unsubLocations = onSnapshot(qLocations, (snapshot) => {
            const locationsList = snapshot.docs.map(doc => docToPlainObject<Location>(doc));
            setLocations(locationsList);
            if(locationsList.length > 0 && !location) {
                setLocation(locationsList[0].name);
            }
        });
        
        const qVendors = query(collection(db, "vendors"), orderBy("name"));
        const unsubVendors = onSnapshot(qVendors, (snapshot) => {
            const vendorsList = snapshot.docs.map(doc => docToPlainObject<Vendor>(doc));
            setVendors(vendorsList);
        });

        const qSpecies = query(collection(db, "species"), orderBy("name"));
        const unsubSpecies = onSnapshot(qSpecies, (snapshot) => {
            const speciesList = snapshot.docs.map(doc => docToPlainObject<Species>(doc));
            setSpecies(speciesList);
        });

        const qEvents = query(collection(db, "events"), orderBy("createdAt", "desc"));
        const unsubEvents = onSnapshot(qEvents, (snapshot) => {
            const eventsList = snapshot.docs.map(doc => docToPlainObject<HACCPEvent>(doc));
            setEvents(eventsList);
        });

        const qOrders = query(collection(db, "purchaseOrders"), orderBy("createdAt", "desc"));
        const unsubOrders = onSnapshot(qOrders, (snapshot) => {
            const ordersList = snapshot.docs.map(doc => docToPlainObject<PurchaseOrder>(doc));
            setPurchaseOrders(ordersList);
        });


        return () => {
            unsubLocations();
            unsubVendors();
            unsubSpecies();
            unsubEvents();
            unsubOrders();
        };
    }, []);
    

    useEffect(() => {
        if (eventType === 'receiving' && !editingEventId) {
            const getJulianDate = (eventDate: Date) => {
                const start = new Date(eventDate.getFullYear(), 0, 0);
                const diff = (eventDate.getTime() - start.getTime()) + ((start.getTimezoneOffset() - eventDate.getTimezoneOffset()) * 60 * 1000);
                const oneDay = 1000 * 60 * 60 * 24;
                const dayOfYear = Math.floor(diff / oneDay);
                const year = String(eventDate.getFullYear()).slice(-2);
                const day = String(dayOfYear).padStart(3, '0');
                return `${year}${day}`;
            };

            const speciesWords = product.trim().toLowerCase().split(/\s+/).filter(Boolean);
            const speciesCode = (speciesWords.length > 1
                ? speciesWords.map(w => w[0]).join('')
                : product.trim().substring(0, 2).toLowerCase()
            );

            const formCode = productForm ? productForm.trim().toLowerCase().charAt(0) : '';

            const vendorWords = supplier.trim().split(/\s+/).filter(Boolean);
            const vendorCode = vendorWords.length > 0 
                ? vendorWords.map((w, i) => i === 0 ? w[0].toUpperCase() : w[0].toLowerCase()).join('')
                : '';

            const dateObject = new Date(date + 'T00:00:00');
            const julianDate = getJulianDate(dateObject);

            const newBatchNumber = `${speciesCode}${formCode}${vendorCode}${julianDate}`;
            
            if (speciesCode || formCode || vendorCode) {
                setBatchNumber(newBatchNumber);
            } else {
                setBatchNumber('');
            }

        } else if (eventType !== 'inventory' && eventType !== 'receiving') {
            setBatchNumber('');
        }
    }, [eventType, editingEventId, product, productForm, supplier, date]);

    const resetFormAndEditingState = useCallback(() => {
        setEditingEventId(null);
        setEventType('receiving');
        setDate(new Date().toISOString().split('T')[0]);
        setTime(new Date().toTimeString().substring(0, 5));
        setBatchNumber('');
        setProduct('');
        setProductForm('');
        setQuantity('');
        setTemperature('32.0');
        setSupplier('');
        setLocation(locations.length > 0 ? locations[0].name : '');
        setFromLocation(locations.length > 0 ? locations[0].name : '');
        setNotes('');
        setAreaCleaned('');
        setSanitizerUsed('');
        setThermometerId('');
        setCalibrationMethod('Ice Point');
        setResult('pass');
        setCorrectiveAction('');
        setEmployeeName('');
        setTrainingTopic('');
        setImageFile(null);
        setImagePreview(null);
        setImageDescription('');
    }, [locations]);

    const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImageFile(file);
            const previewUrl = URL.createObjectURL(file);
            setImagePreview(previewUrl);
        }
    };

    const handleImageAnalysis = async () => {
        if (!imageFile) return;
        setIsAnalyzing(true);
        setImageDescription('Analyzing image...');
        try {
            const ai = new GoogleGenAI({ apiKey: import.meta.env.VITE_GEMINI_API_KEY as string });
            const base64Data = await blobToBase64(imageFile);
            
            const imagePart = {
                inlineData: {
                    mimeType: imageFile.type,
                    data: base64Data,
                },
            };
            const textPart = {
                text: 'Analyze this image related to seafood inventory. Describe what you see, focusing on product type, quality, quantity, or any relevant details for a HACCP log. Is there anything concerning?',
            };
            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash',
                contents: { parts: [imagePart, textPart] },
            });
            
            setImageDescription(response.text);
        } catch (error: any) {
            console.error('Image analysis failed:', error);
            if (error.message?.includes('does not have permission') || error.message?.includes('not found')) {
                 setImageDescription("API Key Error. Please close this window and re-select your API key from the main screen.");
            } else {
                setImageDescription('Analysis failed. Please try again.');
            }
        } finally {
            setIsAnalyzing(false);
        }
    };

    const handleSubmit = async () => {
        // Fix: Use a more flexible type for the event data object to accommodate the
        // Firebase serverTimestamp() FieldValue, which is not a string. Also corrected
        // the logic to only set createdBy on new events.
        const eventData: { [key: string]: any } = {
            eventType,
            date,
            time,
            correctiveAction,
            notes,
            imageDescription,
            ...(editingEventId
                ? { updatedAt: serverTimestamp(), updatedBy: currentUser }
                : { createdAt: serverTimestamp(), createdBy: currentUser }),
        };

        if (imageFile) {
            try {
                const imageUrl = await uploadImage(imageFile, `events/${Date.now()}_${imageFile.name}`);
                eventData.imageUrl = imageUrl;
            } catch (error) {
                alert("Failed to upload image.");
                return;
            }
        } else if (editingEventId && imagePreview) {
             eventData.imageUrl = imagePreview;
        }

        const assignStringField = (key: string, value: string) => {
            if (value && value.trim() !== '') {
                eventData[key] = value.trim();
            }
        };

        const assignNumberField = (key: string, rawValue: string) => {
            if (!rawValue || rawValue.trim() === '') {
                return false;
            }
            const parsed = parseFloat(rawValue);
            if (Number.isNaN(parsed)) {
                return false;
            }
            eventData[key] = parsed;
            return true;
        };

        switch (eventType) {
            case 'receiving':
            case 'sales':
            case 'disposal':
            case 're-sealing':
            case 'inventory': {
                assignStringField('product', product);
                assignStringField('productForm', productForm);
                const hasQuantity = assignNumberField('quantity', quantity);
                if (hasQuantity) {
                    eventData.unit = 'lbs';
                }
                assignStringField('batchNumber', batchNumber);
                if (eventType === 'receiving') {
                    assignNumberField('temperature', temperature);
                    assignStringField('origin', origin);
                    assignNumberField('unitPrice', unitPrice);
                }
                assignStringField('supplier', supplier);
                assignStringField('location', location);
                break;
            }
            case 'relocation': {
                assignStringField('product', product);
                assignStringField('productForm', productForm);
                const hasQuantity = assignNumberField('quantity', quantity);
                if (hasQuantity) {
                    eventData.unit = 'lbs';
                }
                assignStringField('batchNumber', batchNumber);
                assignStringField('fromLocation', fromLocation);
                assignStringField('location', location);
                break;
            }
            case 'sanitation':
                assignStringField('areaCleaned', areaCleaned);
                assignStringField('sanitizerUsed', sanitizerUsed);
                break;
            case 'thermometer-calibration':
                assignStringField('thermometerId', thermometerId);
                assignStringField('calibrationMethod', calibrationMethod);
                assignStringField('result', result);
                break;
            case 'employee-training':
                assignStringField('employeeName', employeeName);
                assignStringField('trainingTopic', trainingTopic);
                break;
        }

        try {
            if (editingEventId) {
                const eventRef = doc(db, 'events', editingEventId);
                await updateDoc(eventRef, eventData);
            } else {
                await addDoc(collection(db, 'events'), eventData);
            }
            resetFormAndEditingState();
            setView('dashboard');
        } catch (error) {
            console.error("Error saving event: ", error);
            alert('Failed to save event.');
        }
    };

    const handleEditEvent = (event: HACCPEvent) => {
        setEditingEventId(event.id);
        setEventType(event.eventType);
        setDate(event.date);
        setTime(event.time);
        setBatchNumber(event.batchNumber || '');
        setProduct(event.product || '');
        setProductForm(event.productForm || '');
        setQuantity(event.quantity?.toString() || '');
        setTemperature(event.temperature?.toString() || '');
        setSupplier(event.supplier || '');
        setLocation(event.location || '');
        setFromLocation(event.fromLocation || '');
        setNotes(event.notes || '');
        setAreaCleaned(event.areaCleaned || '');
        setSanitizerUsed(event.sanitizerUsed || '');
        setThermometerId(event.thermometerId || '');
        setCalibrationMethod(event.calibrationMethod || 'Ice Point');
        setResult(event.result || 'pass');
        setCorrectiveAction(event.correctiveAction || '');
        setEmployeeName(event.employeeName || '');
        setTrainingTopic(event.trainingTopic || '');
        setImageFile(null);
        setImagePreview(event.imageUrl || null);
        setImageDescription(event.imageDescription || '');
        setView('form');
    };
    
    const handleUpdateEventField: UpdateEventField = async (eventId, field, value) => {
        try {
            const eventRef = doc(db, "events", eventId);
            await updateDoc(eventRef, { [field]: value });
            return true;
        } catch (error) {
            console.error("Failed to update event field:", error);
            return false;
        }
    };
    
    const handleOpenSelectKey = async () => {
        if ((window as any).aistudio) {
            await (window as any).aistudio.openSelectKey();
            setApiKeySelected(true);
        }
    };

    const handleStartNewEvent = useCallback(() => {
        resetFormAndEditingState();
        setView('form');
    }, [resetFormAndEditingState, setView]);

    const headerTitle = view === 'form'
        ? (editingEventId ? 'Edit Event' : 'New Event')
        : VIEW_LABELS[view];

    const sidebarView = (view === 'form' ? 'dashboard' : view) as SidebarView;

    const handleNavigate = useCallback(
        (nextView: SidebarView) => {
            setView(nextView);
            if (typeof window !== 'undefined' && window.innerWidth < 768) {
                setIsSidebarCollapsed(true);
            }
        },
        [setIsSidebarCollapsed, setView],
    );

    // Voice Assistant Logic
    const systemInstruction = `You are a voice assistant for a seafood inventory management app. Your primary role is to help users log HACCP events, manage data, and get information through voice commands.
- You can create, update, and inquire about inventory events like receiving, sales, disposal, and more.
- You can manage vendors, species, locations, and purchase orders.
- You can answer questions about the current inventory levels, check stock in specific locations, and summarize recent activities.
- When a user asks a general question or a question that can't be answered by the available functions, provide a helpful, conversational response.
- Today's date is ${new Date().toLocaleDateString()}. Use this for any date-related queries unless the user specifies otherwise.
- Before calling a function, confirm any ambiguous details with the user. For example, if they say "log a sale of salmon", ask for the quantity.
- After a function is successfully called, provide a clear confirmation message to the user, like "Done. I've logged the sale of 15 lbs of Salmon."
- If the user's request is unclear, ask for clarification.
The following functions are available to manage the application state. Call them when the user's intent matches the function's description.
`;

    const functionDeclarations: FunctionDeclaration[] = useMemo(() => [
        {
            name: 'log_haccp_event',
            description: 'Logs a new HACCP event such as receiving, sales, disposal, sanitation, etc.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    eventType: { type: Type.STRING, enum: ['receiving', 'sales', 'disposal', 're-sealing', 'sanitation', 'thermometer-calibration', 'inventory', 'employee-training', 'relocation'] },
                    product: { type: Type.STRING, description: `The specific seafood product. Must be one of: ${species.map(s => s.name).join(', ')}` },
                    productForm: { type: Type.STRING, description: 'The form of the product, e.g., Fillet, Whole.' },
                    quantity: { type: Type.NUMBER, description: 'The weight of the product in pounds (lbs).' },
                    temperature: { type: Type.NUMBER, description: 'The temperature of the product in Fahrenheit.' },
                    supplier: { type: Type.STRING, description: `The supplier of the product. Must be one of: ${vendors.map(v => v.name).join(', ')}` },
                    location: { type: Type.STRING, description: `The storage location for the product. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                    fromLocation: { type: Type.STRING, description: `For relocation events, the location the product is coming from. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                    areaCleaned: { type: Type.STRING, description: 'For sanitation events, the area that was cleaned.' },
                    employeeName: { type: Type.STRING, description: 'For training events, the name of the employee.'},
                    trainingTopic: { type: Type.STRING, description: 'For training events, the topic covered.'},
                    notes: { type: Type.STRING, description: 'Any additional notes for the event.' },
                },
                required: ['eventType']
            }
        },
        {
            name: 'get_inventory_summary',
            description: 'Provides a summary of the current inventory levels for all products.',
            parameters: { type: Type.OBJECT, properties: {} }
        },
        {
            name: 'check_stock_at_location',
            description: 'Checks the inventory of all products at a specific location.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    locationName: { type: Type.STRING, description: `The location to check. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                },
                required: ['locationName']
            }
        },
        {
            name: 'create_purchase_order',
            description: 'Creates a new purchase order plan.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    vendorName: { type: Type.STRING, description: `The name of the vendor. Must be one of: ${vendors.map(v => v.name).join(', ')}` },
                    speciesName: { type: Type.STRING, description: `The species to order. Must be one of: ${species.map(s => s.name).join(', ')}` },
                    quantity: { type: Type.NUMBER, description: 'The quantity to order in pounds (lbs).' },
                    expectedDeliveryDate: { type: Type.STRING, description: 'The expected delivery date in YYYY-MM-DD format.' },
                },
                required: ['vendorName', 'speciesName', 'quantity', 'expectedDeliveryDate']
            }
        },
        {
            name: 'find_events',
            description: 'Finds and retrieves past events based on criteria like product, date, or event type. Returns event IDs that can be used for editing.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    product: { type: Type.STRING, description: `Filter by product name. Must be one of: ${species.map(s => s.name).join(', ')}` },
                    eventType: { type: Type.STRING, enum: ['receiving', 'sales', 'disposal', 're-sealing', 'sanitation', 'thermometer-calibration', 'inventory', 'employee-training', 'relocation'] },
                    date: { type: Type.STRING, description: 'Filter by a specific date (YYYY-MM-DD).' },
                    supplier: { type: Type.STRING, description: `Filter by supplier name. Must be one of: ${vendors.map(v => v.name).join(', ')}` },
                    location: { type: Type.STRING, description: `Filter by location. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                },
            }
        },
        {
            name: 'update_event',
            description: 'Updates an existing HACCP event. Use find_events first to get the event ID, then call this function to update specific fields.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    eventId: { type: Type.STRING, description: 'The ID of the event to update (obtained from find_events).' },
                    product: { type: Type.STRING, description: `The specific seafood product. Must be one of: ${species.map(s => s.name).join(', ')}` },
                    productForm: { type: Type.STRING, description: 'The form of the product, e.g., Fillet, Whole.' },
                    quantity: { type: Type.NUMBER, description: 'The weight of the product in pounds (lbs).' },
                    temperature: { type: Type.NUMBER, description: 'The temperature of the product in Fahrenheit.' },
                    supplier: { type: Type.STRING, description: `The supplier of the product. Must be one of: ${vendors.map(v => v.name).join(', ')}` },
                    location: { type: Type.STRING, description: `The storage location for the product. Must be one of: ${locations.map(l => l.name).join(', ')}` },
                    notes: { type: Type.STRING, description: 'Any additional notes for the event.' },
                },
                required: ['eventId']
            }
        },
        {
            name: 'create_vendor',
            description: 'Creates a new vendor in the system with optional contact details, notes, and products supplied.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    name: { type: Type.STRING, description: "The vendor's business name." },
                    contactPerson: { type: Type.STRING, description: 'Primary contact person at the vendor.' },
                    phone: { type: Type.STRING, description: "Vendor's phone number." },
                    email: { type: Type.STRING, description: "Vendor's email address." },
                    address: { type: Type.STRING, description: "Vendor's physical address." },
                    notes: { type: Type.STRING, description: 'Additional notes about the vendor.' },
                    productsCarried: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'List of products this vendor supplies.' },
                    sourcingLeadTimeDays: { type: Type.NUMBER, description: 'Typical lead time in days for orders from this vendor.' },
                },
                required: ['name']
            }
        },
        {
            name: 'update_vendor',
            description: 'Updates an existing vendor using the vendor name to identify which record to change.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    vendorName: { type: Type.STRING, description: 'The name of the vendor to update. Must match an existing vendor name.' },
                    contactPerson: { type: Type.STRING, description: 'Primary contact person at the vendor.' },
                    phone: { type: Type.STRING, description: "Vendor's phone number." },
                    email: { type: Type.STRING, description: "Vendor's email address." },
                    address: { type: Type.STRING, description: "Vendor's physical address." },
                    notes: { type: Type.STRING, description: 'Additional notes about the vendor.' },
                    productsCarried: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'List of products this vendor supplies.' },
                    sourcingLeadTimeDays: { type: Type.NUMBER, description: 'Typical lead time in days for orders from this vendor.' },
                },
                required: ['vendorName']
            }
        },
        {
            name: 'create_species',
            description: 'Creates a new species in the system with product forms and quality control notes.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    name: { type: Type.STRING, description: 'The species name (e.g., Salmon, Tuna, Shrimp).' },
                    category: { type: Type.STRING, enum: ['Fish', 'Shellfish', 'Other'], description: 'The category of the species.' },
                    productForms: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'List of product forms for this species (e.g., Fillet, Whole, Portion).' },
                    qualityControlNotes: { type: Type.STRING, description: 'Quality control violations and notes for this species.' },
                },
                required: ['name', 'qualityControlNotes']
            }
        },
        {
            name: 'update_species',
            description: 'Updates an existing species using the species name to identify which record to change.',
            parameters: {
                type: Type.OBJECT,
                properties: {
                    speciesName: { type: Type.STRING, description: 'The name of the species to update. Must match an existing species name.' },
                    category: { type: Type.STRING, enum: ['Fish', 'Shellfish', 'Other'], description: 'The category of the species.' },
                    productForms: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'List of product forms for this species (e.g., Fillet, Whole, Portion).' },
                    qualityControlNotes: { type: Type.STRING, description: 'Quality control violations and notes for this species.' },
                },
                required: ['speciesName']
            }
        },
    ], [species, vendors, locations, inventory, inventoryByLocation, purchaseOrders]);
    
    const logHACCPEvent = async (args: any) => {
        const newEvent: Partial<HACCPEvent> = {
            ...args,
            date: args.date || new Date().toISOString().split('T')[0],
            time: new Date().toTimeString().substring(0, 5),
            createdBy: currentUser,
            createdAt: serverTimestamp(),
        };

        if (newEvent.quantity) newEvent.unit = 'lbs';

        await addDoc(collection(db, 'events'), newEvent);
        return `Done. I've logged the ${args.eventType} event.`;
    };

    const getInventorySummary = () => {
        const summary = Object.entries(inventory)
            .map(([product, quantity]) => `${quantity.toFixed(2)} lbs of ${product}`)
            .join(', ');
        return summary ? `Current inventory is: ${summary}.` : 'The inventory is currently empty.';
    };

    const checkStockAtLocation = (args: { locationName: string }) => {
        const { locationName } = args;
        const locationInventory = inventoryByLocation[locationName];
        if (!locationInventory || Object.keys(locationInventory).length === 0) {
            return `There is no inventory currently at ${locationName}.`;
        }
        const summary = Object.entries(locationInventory)
            .map(([product, quantity]) => `${quantity.toFixed(2)} lbs of ${product}`)
            .join(', ');
        return `At ${locationName}, we have: ${summary}.`;
    };

    const createPurchaseOrder = async (args: any) => {
        const vendor = vendors.find(v => v.name.toLowerCase() === args.vendorName.toLowerCase());
        if (!vendor) return `Vendor "${args.vendorName}" not found.`;
        
        const orderData = {
            vendorId: vendor.id,
            vendorName: vendor.name,
            species: args.speciesName,
            quantity: args.quantity,
            unit: 'lbs',
            expectedDeliveryDate: args.expectedDeliveryDate,
            status: 'planning',
            createdAt: serverTimestamp(),
        };

        await addDoc(collection(db, "purchaseOrders"), orderData);
        return `Okay, I've created a planned purchase order for ${args.quantity} lbs of ${args.speciesName} from ${args.vendorName}.`;
    };

    const findEvents = (args: any) => {
        let results = [...events];
        if (args.product) results = results.filter(e => e.product?.toLowerCase() === args.product.toLowerCase());
        if (args.eventType) results = results.filter(e => e.eventType === args.eventType);
        if (args.date) results = results.filter(e => e.date === args.date);
        if (args.supplier) results = results.filter(e => e.supplier?.toLowerCase() === args.supplier.toLowerCase());
        if (args.location) results = results.filter(e => e.location?.toLowerCase() === args.location.toLowerCase());

        if (results.length === 0) return 'I could not find any events matching your criteria.';

        const summary = results.slice(0, 5).map(e => {
            let details = `Event ID ${e.id}: On ${e.date}, a ${e.eventType} event`;
            if (e.product) details += ` for ${e.quantity || ''} ${e.unit || ''} of ${e.product}`;
            if (e.supplier) details += ` from ${e.supplier}`;
            if (e.location) details += ` at ${e.location}`;
            return details;
        }).join('. ');

        return `I found ${results.length} events. Here are the first few: ${summary}. Use the Event ID to update any of these events.`;
    };

    const updateEvent = async (args: any) => {
        const { eventId, ...updates } = args;

        if (!eventId) return 'Error: Event ID is required to update an event.';

        // Find the event to make sure it exists
        const event = events.find(e => e.id === eventId);
        if (!event) return `Error: Could not find event with ID ${eventId}.`;

        // Only update fields that were provided
        const updateData: any = {};
        if (updates.product !== undefined) updateData.product = updates.product;
        if (updates.productForm !== undefined) updateData.productForm = updates.productForm;
        if (updates.quantity !== undefined) {
            updateData.quantity = updates.quantity;
            updateData.unit = 'lbs';
        }
        if (updates.temperature !== undefined) updateData.temperature = updates.temperature;
        if (updates.supplier !== undefined) updateData.supplier = updates.supplier;
        if (updates.location !== undefined) updateData.location = updates.location;
        if (updates.notes !== undefined) updateData.notes = updates.notes;

        // Add updatedAt timestamp
        updateData.updatedAt = serverTimestamp();

        try {
            await updateDoc(doc(db, 'events', eventId), updateData);
            return `Done. I've updated the ${event.eventType} event from ${event.date}.`;
        } catch (error: any) {
            return `Error updating event: ${error.message}`;
        }
    };

    const createVendor = async (args: any) => {
        if (!args?.name) return 'Error: Vendor name is required to create a vendor.';

        const vendorData: Partial<Vendor> = {
            name: args.name,
        };

        if (args.contactPerson !== undefined) vendorData.contactPerson = args.contactPerson;
        if (args.phone !== undefined) vendorData.phone = args.phone;
        if (args.email !== undefined) vendorData.email = args.email;
        if (args.address !== undefined) vendorData.address = args.address;
        if (args.notes !== undefined) vendorData.notes = args.notes;

        if (args.productsCarried !== undefined) {
            if (Array.isArray(args.productsCarried)) {
                vendorData.productsCarried = args.productsCarried;
            } else {
                return 'Error: productsCarried must be an array of strings.';
            }
        }

        if (args.sourcingLeadTimeDays !== undefined) {
            const leadTime = Number(args.sourcingLeadTimeDays);
            if (Number.isNaN(leadTime)) {
                return 'Error: sourcingLeadTimeDays must be a number.';
            }
            vendorData.sourcingLeadTimeDays = leadTime;
        }

        try {
            await addDoc(collection(db, 'vendors'), vendorData);
            return `Done. I've created the vendor ${args.name}.`;
        } catch (error: any) {
            return `Error creating vendor: ${error.message}`;
        }
    };

    const updateVendor = async (args: any) => {
        const { vendorName, ...updates } = args;

        if (!vendorName) return 'Error: Vendor name is required to update a vendor.';

        const vendor = vendors.find(v => v.name.toLowerCase() === vendorName.toLowerCase());
        if (!vendor) return `Error: Could not find vendor named ${vendorName}.`;

        const updateData: Partial<Vendor> = {};

        if (updates.contactPerson !== undefined) updateData.contactPerson = updates.contactPerson;
        if (updates.phone !== undefined) updateData.phone = updates.phone;
        if (updates.email !== undefined) updateData.email = updates.email;
        if (updates.address !== undefined) updateData.address = updates.address;
        if (updates.notes !== undefined) updateData.notes = updates.notes;

        if (updates.productsCarried !== undefined) {
            if (Array.isArray(updates.productsCarried)) {
                updateData.productsCarried = updates.productsCarried;
            } else {
                return 'Error: productsCarried must be an array of strings.';
            }
        }

        if (updates.sourcingLeadTimeDays !== undefined) {
            const leadTime = Number(updates.sourcingLeadTimeDays);
            if (Number.isNaN(leadTime)) {
                return 'Error: sourcingLeadTimeDays must be a number.';
            }
            updateData.sourcingLeadTimeDays = leadTime;
        }

        if (Object.keys(updateData).length === 0) {
            return 'Error: No fields provided to update.';
        }

        try {
            await updateDoc(doc(db, 'vendors', vendor.id), updateData);
            return `Done. I've updated the vendor ${vendorName}.`;
        } catch (error: any) {
            return `Error updating vendor: ${error.message}`;
        }
    };

    const createSpecies = async (args: any) => {
        if (!args?.name) return 'Error: Species name is required to create a species.';
        if (!args?.qualityControlNotes) return 'Error: Quality control notes are required to create a species.';

        // Check for duplicate species name
        const normalizedNew = args.name.trim().toLowerCase();
        const existingSpecies = species.find(s => s.name.trim().toLowerCase() === normalizedNew);
        if (existingSpecies) {
            return `Error: A species named "${existingSpecies.name}" already exists. Please use a different name or update the existing species.`;
        }
        args.name = args.name.trim();

        const speciesData: Partial<Species> = {
            name: args.name,
            qualityControlNotes: args.qualityControlNotes,
            productForms: [],
        };

        if (args.category) {
            const normalizedCategory = args.category.trim().charAt(0).toUpperCase() + args.category.trim().slice(1).toLowerCase();
            const allowedCategories = ['Fish', 'Shellfish', 'Other'];
            if (allowedCategories.includes(normalizedCategory)) {
                speciesData.category = normalizedCategory as 'Fish' | 'Shellfish' | 'Other';
            } else {
                return `Error: Invalid category "${args.category}". Acceptable values are: ${allowedCategories.join(', ')}.`;
            }
        }

        if (args.productForms !== undefined) {
            if (Array.isArray(args.productForms)) {
                speciesData.productForms = args.productForms
                    .map(item => typeof item === 'string' ? item.trim() : '')
                    .filter(item => item !== '');
            } else {
                return 'Error: productForms must be an array of strings.';
            }
        }

        try {
            await addDoc(collection(db, 'species'), speciesData);
            return `Done. I've created the species ${args.name}.`;
        } catch (error: any) {
            return `Error creating species: ${error.message}`;
        }
    };

    const updateSpecies = async (args: any) => {
        const { speciesName, ...updates } = args;

        if (!speciesName) return 'Error: Species name is required to update a species.';

        // Find all matches to check for duplicates
        const matchingSpecies = species.filter(s => s.name.toLowerCase() === speciesName.toLowerCase());
        if (matchingSpecies.length === 0) {
            return `Error: Could not find species named ${speciesName}.`;
        }
        if (matchingSpecies.length > 1) {
            return `Error: Multiple species found with name "${speciesName}". Please contact support to resolve this data inconsistency.`;
        }

        const speciesRecord = matchingSpecies[0];
        const updateData: Partial<Species> = {};

        if (updates.category) {
            const normalizedCategory = updates.category.trim().charAt(0).toUpperCase() + updates.category.trim().slice(1).toLowerCase();
            const allowedCategories = ['Fish', 'Shellfish', 'Other'];
            if (allowedCategories.includes(normalizedCategory)) {
                updateData.category = normalizedCategory as 'Fish' | 'Shellfish' | 'Other';
            } else {
                return `Error: Invalid category "${updates.category}". Acceptable values are: ${allowedCategories.join(', ')}.`;
            }
        }

        if (updates.qualityControlNotes !== undefined) {
            updateData.qualityControlNotes = updates.qualityControlNotes;
        }

        if (updates.productForms !== undefined) {
            if (Array.isArray(updates.productForms)) {
                updateData.productForms = updates.productForms
                    .map(item => typeof item === 'string' ? item.trim() : '')
                    .filter(item => item !== '');
            } else {
                return 'Error: productForms must be an array of strings.';
            }
        }

        if (Object.keys(updateData).length === 0) {
            return 'Error: No fields provided to update.';
        }

        try {
            await updateDoc(doc(db, 'species', speciesRecord.id), updateData);
            return `Done. I've updated the species ${speciesName}.`;
        } catch (error: any) {
            return `Error updating species: ${error.message}`;
        }
    };

    const processFunctionCall = async (fc: any) => {
        let result: any;
        let functionResponseText = '';
        const functionName = fc.name;
        const args = fc.args;

        switch (functionName) {
            case 'log_haccp_event':
                functionResponseText = await logHACCPEvent(args);
                result = { result: "Event logged successfully." };
                break;
            case 'get_inventory_summary':
                functionResponseText = getInventorySummary();
                result = { result: "Displayed inventory summary." };
                break;
            case 'check_stock_at_location':
                functionResponseText = checkStockAtLocation(args);
                result = { result: "Displayed stock for location." };
                break;
            case 'create_purchase_order':
                functionResponseText = await createPurchaseOrder(args);
                result = { result: "Purchase order created." };
                break;
            case 'find_events':
                functionResponseText = findEvents(args);
                result = { result: "Found and summarized events." };
                break;
            case 'update_event':
                functionResponseText = await updateEvent(args);
                result = { result: "Event updated successfully." };
                break;
            case 'create_vendor':
                functionResponseText = await createVendor(args);
                result = functionResponseText.startsWith('Error:')
                    ? { error: functionResponseText }
                    : { result: "Vendor created successfully." };
                break;
            case 'update_vendor':
                functionResponseText = await updateVendor(args);
                result = functionResponseText.startsWith('Error:')
                    ? { error: functionResponseText }
                    : { result: "Vendor updated successfully." };
                break;
            case 'create_species':
                functionResponseText = await createSpecies(args);
                result = functionResponseText.startsWith('Error:')
                    ? { error: functionResponseText }
                    : { result: "Species created successfully." };
                break;
            case 'update_species':
                functionResponseText = await updateSpecies(args);
                result = functionResponseText.startsWith('Error:')
                    ? { error: functionResponseText }
                    : { result: "Species updated successfully." };
                break;
            default:
                console.warn(`Unknown function call: ${functionName}`);
                result = { error: "Unknown function" };
                functionResponseText = `Sorry, I can't do that.`;
        }

        return { functionResponseText, result };
    };

    const startSession = useCallback(async () => {
        setStatus('Connecting...');
        setConversation([]);
        currentInputTranscriptionRef.current = '';
        currentOutputTranscriptionRef.current = '';

        if (!apiKeySelected) {
            setStatus('Please select an API key first.');
            return;
        }

        try {
            const ai = new GoogleGenAI({ apiKey: import.meta.env.VITE_GEMINI_API_KEY });
            
            inputAudioContextRef.current = new AudioContext({ sampleRate: 16000 });
            outputAudioContextRef.current = new AudioContext({ sampleRate: 24000 });
            
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            streamRef.current = stream;

            sessionPromiseRef.current = ai.live.connect({
                model: 'gemini-2.5-flash-native-audio-preview-09-2025',
                callbacks: {
                    onopen: () => {
                        setStatus('Listening... Speak now.');
                        setIsSessionActive(true);

                        const source = inputAudioContextRef.current!.createMediaStreamSource(stream);
                        mediaStreamSourceRef.current = source;
                        
                        // Use ScriptProcessor for wider browser support
                        const scriptProcessor = inputAudioContextRef.current!.createScriptProcessor(4096, 1, 1);
                        scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
                            const inputData = audioProcessingEvent.inputBuffer.getChannelData(0);
                            const pcmBlob = createBlob(inputData);
                            sessionPromiseRef.current?.then((session) => {
                                session.sendRealtimeInput({ media: pcmBlob });
                            });
                        };
                        source.connect(scriptProcessor);
                        scriptProcessor.connect(inputAudioContextRef.current!.destination); // This is necessary for onaudioprocess to fire in some browsers.
                    },
                    onmessage: async (message: LiveServerMessage) => {
                        if (message.serverContent?.outputTranscription) {
                            const text = message.serverContent.outputTranscription.text;
                            currentOutputTranscriptionRef.current += text;
                        } else if (message.serverContent?.inputTranscription) {
                            const text = message.serverContent.inputTranscription.text;
                            currentInputTranscriptionRef.current += text;
                        }

                        if (message.serverContent?.turnComplete) {
                             const fullInput = currentInputTranscriptionRef.current;
                             const fullOutput = currentOutputTranscriptionRef.current;
                            
                             if (fullInput) {
                                setConversation(prev => [...prev, createConversationTurn('user', fullInput)]);
                             }
                             if (fullOutput) {
                                setConversation(prev => [...prev, createConversationTurn('model', fullOutput)]);
                             }
                             
                             currentInputTranscriptionRef.current = '';
                             currentOutputTranscriptionRef.current = '';
                        }
                        
                        if(message.toolCall) {
                            for (const fc of message.toolCall.functionCalls) {
                                const { functionResponseText, result } = await processFunctionCall(fc);
                                setConversation(prev => [...prev, createConversationTurn('model', functionResponseText)]);
                                
                                sessionPromiseRef.current?.then((session) => {
                                    session.sendToolResponse({
                                        functionResponses: {
                                            id: fc.id,
                                            name: fc.name,
                                            response: result,
                                        }
                                    });
                                });
                            }
                        }

                        const base64Audio = message.serverContent?.modelTurn?.parts[0]?.inlineData?.data;
                        if (base64Audio) {
                            const outputContext = outputAudioContextRef.current!;
                            nextStartTimeRef.current = Math.max(nextStartTimeRef.current, outputContext.currentTime);
                            
                            const audioBuffer = await decodeAudioData(
                                decode(base64Audio),
                                outputContext,
                                24000,
                                1,
                            );

                            const source = outputContext.createBufferSource();
                            source.buffer = audioBuffer;
                            source.connect(outputContext.destination);
                            
                            source.addEventListener('ended', () => {
                                sourcesRef.current.delete(source);
                            });
                            
                            source.start(nextStartTimeRef.current);
                            nextStartTimeRef.current += audioBuffer.duration;
                            sourcesRef.current.add(source);
                        }
                        
                        const interrupted = message.serverContent?.interrupted;
                        if (interrupted) {
                            for (const source of sourcesRef.current.values()) {
                                source.stop();
                                sourcesRef.current.delete(source);
                            }
                            nextStartTimeRef.current = 0;
                        }

                    },
                    onerror: (e: ErrorEvent) => {
                        console.error('Session error:', e);
                        setStatus('Connection error. Please try again.');
                        if (e.message?.includes('Requested entity was not found')) {
                            setApiKeySelected(false);
                            setStatus('API Key Error. Please re-select your key.');
                        }
                        stopSession();
                    },
                    onclose: (e: CloseEvent) => {
                       // This is an expected event when stopSession() is called.
                       // We can reset the state here.
                       setIsSessionActive(false);
                       setStatus('Idle. Press Start to begin.');
                    },
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    outputAudioTranscription: {},
                    inputAudioTranscription: {},
                    tools: [{ functionDeclarations }],
                    systemInstruction: systemInstruction,
                },
            });

        } catch (error) {
            console.error('Failed to start session:', error);
            setStatus('Failed to start. Check mic permissions.');
            stopSession();
        }
    }, [apiKeySelected, functionDeclarations, systemInstruction]);

    const stopSession = useCallback(() => {
        sessionPromiseRef.current?.then((session) => {
            session.close();
        });
        sessionPromiseRef.current = null;
        
        streamRef.current?.getTracks().forEach(track => track.stop());
        streamRef.current = null;
        
        mediaStreamSourceRef.current?.disconnect();
        mediaStreamSourceRef.current = null;
        
        workletNodeRef.current?.disconnect();
        workletNodeRef.current = null;

        inputAudioContextRef.current?.close().catch(console.error);
        inputAudioContextRef.current = null;

        setIsSessionActive(false);
        setStatus('Idle. Press Start to begin.');
    }, []);
    
    const handleCaptureAndSave = (events: Partial<HACCPEvent>[]) => {
        const batch = writeBatch(db);
        const newSpecies = new Set<string>();
        const newVendors = new Set<string>();
        const newLocations = new Set<string>();

        const existingSpecies = new Set(species.map(s => s.name.toLowerCase()));
        const existingVendors = new Set(vendors.map(v => v.name.toLowerCase()));
        const existingLocations = new Set(locations.map(l => l.name.toLowerCase()));

        events.forEach(event => {
            const eventRef = doc(collection(db, "events"));
            const completeEvent = {
                ...event,
                time: new Date().toTimeString().substring(0, 5),
                createdBy: currentUser,
                createdAt: serverTimestamp(),
            };
            if(!completeEvent.date) completeEvent.date = new Date().toISOString().split('T')[0];
            batch.set(eventRef, completeEvent);

            if (event.product && !existingSpecies.has(event.product.toLowerCase())) newSpecies.add(event.product);
            if (event.supplier && !existingVendors.has(event.supplier.toLowerCase())) newVendors.add(event.supplier);
            if (event.location && !existingLocations.has(event.location.toLowerCase())) newLocations.add(event.location);
        });
        
        newSpecies.forEach(name => batch.set(doc(collection(db, "species")), { name, productForms: [], qualityControlNotes: 'N/A' }));
        newVendors.forEach(name => batch.set(doc(collection(db, "vendors")), { name }));
        newLocations.forEach(name => batch.set(doc(collection(db, "locations")), { name }));

        batch.commit().then(() => {
            setIsCameraModalOpen(false);
            setView('dashboard');
        }).catch(error => {
            console.error("Failed to save batch data:", error);
            alert("Failed to save data. Check console for details.");
        });
    };
    
    const handleImportAndSave = (events: Partial<HACCPEvent>[]) => {
        handleCaptureAndSave(events); // Re-use the same batch save logic
        setIsImportModalOpen(false);
    };

    const renderView = () => {
        switch (view) {

case 'form': {
    const formTitle = editingEventId ? 'Edit HACCP Event' : 'New HACCP Event';
    const productEventTypes: EventType[] = ['receiving', 'sales', 'disposal', 're-sealing', 'inventory', 'relocation'];
    const showProductFields = productEventTypes.includes(eventType);
    const showBatchField = eventType === 'receiving' || eventType === 'inventory';
    const showSupplierField = eventType === 'receiving';
    const showLocationField = ['receiving', 'sales', 'disposal', 'relocation', 'inventory'].includes(eventType);
    const locationLabel = eventType === 'relocation' ? 'To Location' : 'Location';

    return (
        <Paper variant="outlined" sx={{ p: { xs: 3, md: 4 }, maxWidth: 720, width: '100%', mx: 'auto', borderRadius: 3 }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                <Typography variant="h5" fontWeight={700} color="text.primary">
                    {formTitle}
                </Typography>
                {editingEventId && (
                    <Button variant="text" size="small" onClick={resetFormAndEditingState} sx={{ textTransform: 'none' }}>
                        Cancel Edit
                    </Button>
                )}
            </Stack>

            <Stack spacing={3}>
                <TextField
                    select
                    label="Event Type"
                    value={eventType}
                    onChange={(event) => setEventType(event.target.value as EventType)}
                    fullWidth
                >
                    <MenuItem value="receiving">Receiving</MenuItem>
                    <MenuItem value="sales">Sales</MenuItem>
                    <MenuItem value="disposal">Disposal</MenuItem>
                    <MenuItem value="re-sealing">Re-sealing</MenuItem>
                    <MenuItem value="relocation">Relocation</MenuItem>
                    <MenuItem value="inventory">Inventory Count</MenuItem>
                    <MenuItem value="sanitation">Sanitation</MenuItem>
                    <MenuItem value="thermometer-calibration">Thermometer Calibration</MenuItem>
                    <MenuItem value="employee-training">Employee Training</MenuItem>
                </TextField>

                <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                        <TextField
                            label="Date"
                            type="date"
                            value={date}
                            onChange={(event) => setDate(event.target.value)}
                            fullWidth
                            InputLabelProps={{ shrink: true }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                        <TextField
                            label="Time"
                            type="time"
                            value={time}
                            onChange={(event) => setTime(event.target.value)}
                            fullWidth
                            InputLabelProps={{ shrink: true }}
                        />
                    </Grid>
                </Grid>

                {showProductFields && (
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                select
                                label="Product/Species"
                                value={product}
                                onChange={(event) => setProduct(event.target.value)}
                                fullWidth
                            >
                                <MenuItem value="">-- Select Species --</MenuItem>
                                {species.map((entry) => (
                                    <MenuItem key={entry.id} value={entry.name}>
                                        {entry.name}
                                    </MenuItem>
                                ))}
                            </TextField>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                select
                                label="Product Form"
                                value={productForm}
                                onChange={(event) => setProductForm(event.target.value)}
                                fullWidth
                                disabled={!product}
                            >
                                <MenuItem value="">-- Select Form --</MenuItem>
                                {(species.find((entry) => entry.name === product)?.productForms || []).map((form) => (
                                    <MenuItem key={form} value={form}>
                                        {form}
                                    </MenuItem>
                                ))}
                            </TextField>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                label="Quantity (lbs)"
                                type="number"
                                value={quantity}
                                onChange={(event) => setQuantity(event.target.value)}
                                fullWidth
                                placeholder="e.g., 50.5"
                            />
                        </Grid>
                        {eventType === 'receiving' && (
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    label="Temperature (°F)"
                                    type="number"
                                    value={temperature}
                                    onChange={(event) => setTemperature(event.target.value)}
                                    fullWidth
                                />
                            </Grid>
                        )}
                        {eventType === 'receiving' && (
                            <>
                                <Grid item xs={12} sm={6}>
                                    <TextField
                                        label="Unit Price ($)"
                                        type="number"
                                        value={unitPrice}
                                        onChange={(event) => setUnitPrice(event.target.value)}
                                        fullWidth
                                        placeholder="e.g., 22.50"
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                    <TextField
                                        label="Origin"
                                        value={origin}
                                        onChange={(event) => setOrigin(event.target.value)}
                                        fullWidth
                                        placeholder="e.g., Alaska"
                                    />
                                </Grid>
                            </>
                        )}
                        {showBatchField && (
                            <Grid item xs={12}>
                                <TextField
                                    label="Batch Number"
                                    value={batchNumber}
                                    onChange={(event) => setBatchNumber(event.target.value)}
                                    fullWidth
                                    placeholder="Auto-generated or manual"
                                    InputProps={{ sx: { fontFamily: 'monospace' } }}
                                />
                            </Grid>
                        )}
                        {showSupplierField && (
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    select
                                    label="Supplier"
                                    value={supplier}
                                    onChange={(event) => setSupplier(event.target.value)}
                                    fullWidth
                                >
                                    <MenuItem value="">-- Select Vendor --</MenuItem>
                                    {vendors.map((vendor) => (
                                        <MenuItem key={vendor.id} value={vendor.name}>
                                            {vendor.name}
                                        </MenuItem>
                                    ))}
                                </TextField>
                            </Grid>
                        )}
                        {eventType === 'relocation' && (
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    select
                                    label="From Location"
                                    value={fromLocation}
                                    onChange={(event) => setFromLocation(event.target.value)}
                                    fullWidth
                                >
                                    {locations.map((entry) => (
                                        <MenuItem key={entry.id} value={entry.name}>
                                            {entry.name}
                                        </MenuItem>
                                    ))}
                                </TextField>
                            </Grid>
                        )}
                        {showLocationField && (
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    select
                                    label={locationLabel}
                                    value={location}
                                    onChange={(event) => setLocation(event.target.value)}
                                    fullWidth
                                >
                                    <MenuItem value="">-- Select Location --</MenuItem>
                                    {locations.map((entry) => (
                                        <MenuItem key={entry.id} value={entry.name}>
                                            {entry.name}
                                        </MenuItem>
                                    ))}
                                </TextField>
                            </Grid>
                        )}
                    </Grid>
                )}

                {eventType === 'sanitation' && (
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                label="Area/Equipment Cleaned"
                                value={areaCleaned}
                                onChange={(event) => setAreaCleaned(event.target.value)}
                                fullWidth
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                label="Sanitizer Used"
                                value={sanitizerUsed}
                                onChange={(event) => setSanitizerUsed(event.target.value)}
                                fullWidth
                            />
                        </Grid>
                    </Grid>
                )}

                {eventType === 'thermometer-calibration' && (
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                label="Thermometer ID"
                                value={thermometerId}
                                onChange={(event) => setThermometerId(event.target.value)}
                                fullWidth
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                select
                                label="Calibration Method"
                                value={calibrationMethod}
                                onChange={(event) => setCalibrationMethod(event.target.value)}
                                fullWidth
                            >
                                <MenuItem value="Ice Point">Ice Point</MenuItem>
                                <MenuItem value="Boiling Point">Boiling Point</MenuItem>
                            </TextField>
                        </Grid>
                        <Grid item xs={12}>
                            <FormControl component="fieldset">
                                <FormLabel>Result</FormLabel>
                                <RadioGroup
                                    row
                                    value={result}
                                    onChange={(event) => setResult(event.target.value as 'pass' | 'fail')}
                                >
                                    <FormControlLabel value="pass" control={<Radio />} label="Pass" />
                                    <FormControlLabel value="fail" control={<Radio />} label="Fail" />
                                </RadioGroup>
                            </FormControl>
                        </Grid>
                    </Grid>
                )}

                {eventType === 'employee-training' && (
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                label="Employee Name"
                                value={employeeName}
                                onChange={(event) => setEmployeeName(event.target.value)}
                                fullWidth
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                label="Training Topic"
                                value={trainingTopic}
                                onChange={(event) => setTrainingTopic(event.target.value)}
                                fullWidth
                            />
                        </Grid>
                    </Grid>
                )}

                <Stack spacing={2}>
                    <Typography variant="body2" fontWeight={600} color="text.secondary">
                        Attach Image
                    </Typography>
                    <Stack direction="row" spacing={2} alignItems="center">
                        <Box
                            sx={{
                                width: 80,
                                height: 80,
                                borderRadius: 2,
                                bgcolor: 'background.default',
                                border: (theme) => `1px dashed ${theme.palette.divider}`,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                overflow: 'hidden',
                            }}
                        >
                            {imagePreview ? (
                                <img src={imagePreview} alt="Preview" style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                            ) : (
                                <PhotoIcon />
                            )}
                        </Box>
                        <Stack spacing={1} flex={1}>
                            <Button variant="outlined" component="label" sx={{ textTransform: 'none', alignSelf: 'flex-start' }}>
                                Upload Image
                                <input hidden type="file" accept="image/*" onChange={handleImageSelect} />
                            </Button>
                            {imageFile && (
                                <Button
                                    variant="text"
                                    color="primary"
                                    size="small"
                                    startIcon={<SparklesIcon />}
                                    onClick={handleImageAnalysis}
                                    disabled={isAnalyzing}
                                    sx={{ alignSelf: 'flex-start', textTransform: 'none' }}
                                >
                                    {isAnalyzing ? 'Analyzing…' : 'Analyze with AI'}
                                </Button>
                            )}
                        </Stack>
                    </Stack>
                    {imageDescription && (
                        <TextField
                            label="AI Analysis / Image Description"
                            value={imageDescription}
                            onChange={(event) => setImageDescription(event.target.value)}
                            fullWidth
                            multiline
                            minRows={3}
                        />
                    )}
                </Stack>

                <TextField
                    label="Notes"
                    value={notes}
                    onChange={(event) => setNotes(event.target.value)}
                    fullWidth
                    multiline
                    minRows={3}
                />
                <TextField
                    label="Corrective Action"
                    value={correctiveAction}
                    onChange={(event) => setCorrectiveAction(event.target.value)}
                    fullWidth
                    multiline
                    minRows={3}
                />

                <Stack direction="row" justifyContent="flex-end">
                    <Button variant="contained" onClick={handleSubmit} sx={{ textTransform: 'none' }}>
                        {editingEventId ? 'Update Event' : 'Save Event'}
                    </Button>
                </Stack>
            </Stack>
        </Paper>
    );
}
            case 'dashboard':
                return <Dashboard events={events} species={species} vendors={vendors} locations={locations} onEditEvent={handleEditEvent} onDraftEmail={setEmailContext} onUpdateEventField={handleUpdateEventField}/>;
            case 'haccp':
                return <HaccpLogsView />;
            case 'inventory':
                return <InventoryManagement events={events} species={species} vendors={vendors} />;
            case 'reports':
                return <ReportsView />;
            case 'settings':
                return <SettingsView onNavigate={setView} />;
            case 'calendar':
                return <CalendarView events={events} onAddNewEvent={(date) => { resetFormAndEditingState(); setDate(date); setView('form');}} />;
            case 'vendors':
                return <VendorsView />;
            case 'species':
                return <SpeciesView />;
            case 'locations':
                return <LocationsView events={events} />;
            case 'orders':
                return <OrdersView purchaseOrders={purchaseOrders} vendors={vendors} species={species} onDraftEmail={setEmailContext} />;
            case 'temperature':
                return <TemperatureView />;
            case 'lot-tracking':
                return <LotTrackingView />;
            default:
                return <div>Select a view</div>;
        }
    };

    // Show loading spinner while checking auth
    if (authLoading) {
        return (
            <AppThemeProvider>
                <Box sx={{ minHeight: '100dvh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Stack spacing={2} alignItems="center">
                        <CircularProgress color="primary" />
                        <Typography variant="body2" color="text.secondary">
                            Loading...
                        </Typography>
                    </Stack>
                </Box>
            </AppThemeProvider>
        );
    }

    // Show login screen if not authenticated
    if (!user) {
        return (
            <AppThemeProvider>
                <Login onLoginSuccess={() => {}} />
            </AppThemeProvider>
        );
    }

    return (
        <AppThemeProvider>
            <>
                <Box sx={{ display: 'flex' }}>
                    <Sidebar
                        id="main-navigation"
                        currentView={sidebarView}
                        onNavigate={handleNavigate}
                        collapsed={isSidebarCollapsed}
                        onCollapseChange={setIsSidebarCollapsed}
                    />
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            minHeight: '100dvh',
                            flexGrow: 1,
                            bgcolor: 'background.default',
                            ml: { xs: 0, md: isSidebarCollapsed ? '4rem' : '16rem' },
                            transition: (theme) => theme.transitions.create('margin-left'),
                        }}
                    >
                        <Box
                            component="header"
                            sx={{
                                position: 'sticky',
                                top: 0,
                                zIndex: 15,
                                borderBottom: '1px solid',
                                borderColor: 'divider',
                                bgcolor: 'background.paper',
                                px: { xs: 2, md: 3 },
                                py: 1.5,
                            }}
                        >
                            <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '100%' }}>
                                <IconButton
                                    onClick={() => setIsSidebarCollapsed(false)}
                                    aria-label="Open navigation"
                                    aria-controls="main-navigation"
                                    sx={{ display: { xs: 'inline-flex', md: 'none' } }}
                                >
                                    <HamburgerIcon className="h-5 w-5" />
                                </IconButton>
                                <Box>
                                    <Typography variant="h5" fontWeight={700}>
                                        {headerTitle}
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{ display: { xs: 'none', sm: 'block' } }}
                                    >
                                        Manage your operations from here.
                                    </Typography>
                                </Box>
                                <Stack
                                    direction="row"
                                    spacing={1.5}
                                    alignItems="center"
                                    sx={{
                                        flexShrink: 0,
                                        flexWrap: { xs: 'wrap', sm: 'nowrap' },
                                        rowGap: 1,
                                        columnGap: 1.5,
                                    }}
                                >
                                    <Button variant="contained" onClick={handleStartNewEvent}>
                                        New Event
                                    </Button>
                                    <Button variant="outlined" color="inherit" onClick={() => signOut(auth)}>
                                        Sign Out
                                    </Button>
                                </Stack>
                                <Box sx={{ flex: 1 }} />
                                <TopbarActions userEmail={user?.email ?? undefined} />
                            </Stack>
                        </Box>
                        <Box
                            component="main"
                            sx={{
                                flexGrow: 1,
                                px: { xs: 2, md: 3 },
                                py: { xs: 2, md: 3 },
                                display: 'flex',
                                flexDirection: 'column',
                                bgcolor: 'background.default',
                            }}
                        >
                            <Stack spacing={4} sx={{ maxWidth: '90rem', mx: 'auto', width: '100%' }}>
                                {renderView()}
                            </Stack>
                        </Box>
                    </Box>
                </Box>

                <Box
                    sx={{
                        position: 'fixed',
                        bottom: 24,
                        right: 24,
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 2,
                        alignItems: 'center',
                        zIndex: 20,
                    }}
                >
                    <Tooltip title="Scan Document" placement="left">
                        <Fab color="default" onClick={() => setIsCameraModalOpen(true)}>
                            <Box sx={{ display: 'flex', '& svg': { width: 28, height: 28 } }}>
                                <CameraIcon />
                            </Box>
                        </Fab>
                    </Tooltip>
                    <Tooltip title={isSessionActive ? 'Stop Voice Session' : 'Start Voice Session'} placement="left">
                        <Fab
                            color={isSessionActive ? 'error' : 'primary'}
                            onClick={() => (isSessionActive ? stopSession() : startSession())}
                        >
                            <Box sx={{ display: 'flex', '& svg': { width: 28, height: 28 } }}>
                                {isSessionActive ? <StopIcon /> : <MicrophoneIcon />}
                            </Box>
                        </Fab>
                    </Tooltip>
                </Box>

                {isSessionActive && (
                    <Box
                        sx={(theme) => ({
                            position: 'fixed',
                            bottom: { xs: 136, sm: 112 },
                            right: { xs: 16, sm: 24 },
                            left: { xs: 16, sm: 'auto' },
                            width: { xs: 'auto', sm: '100%' },
                            maxWidth: { sm: '24rem' },
                            bgcolor: alpha(theme.palette.background.default, 0.95),
                            borderRadius: 3,
                            boxShadow: 6,
                            p: 2,
                            zIndex: 10,
                            ...theme.applyStyles('dark', {
                                bgcolor: alpha(theme.palette.background.default, 0.85),
                            }),
                        })}
                    >
                        <Stack spacing={2}>
                            <StatusIndicator status={status} isRecording={isSessionActive} />
                            <ConversationLog turns={conversation} />
                        </Stack>
                    </Box>
                )}

                {!apiKeySelected && (
                    <Dialog open maxWidth="xs" fullWidth>
                        <DialogTitle>API Key Required</DialogTitle>
                        <DialogContent dividers>
                            <Stack spacing={2} textAlign="center">
                                <Typography variant="body2">
                                    This application requires a Google AI API key to function. Please select a key to continue.
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    For information on billing, visit{' '}
                                    <Link
                                        href="https://ai.google.dev/gemini-api/docs/billing"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                    >
                                        ai.google.dev/gemini-api/docs/billing
                                    </Link>.
                                </Typography>
                            </Stack>
                        </DialogContent>
                        <DialogActions>
                            <Button variant="contained" onClick={handleOpenSelectKey}>
                                Select API Key
                            </Button>
                        </DialogActions>
                    </Dialog>
                )}

                {isEmailModalOpen && emailContext && (
                    <EmailDraftModal
                        isOpen={isEmailModalOpen}
                        onClose={() => setIsEmailModalOpen(false)}
                        context={emailContext}
                    />
                )}

                {isCameraModalOpen && (
                    <CameraModal
                        species={species}
                        vendors={vendors}
                        locations={locations}
                        onClose={() => setIsCameraModalOpen(false)}
                        onSave={handleCaptureAndSave}
                    />
                )}

                {isImportModalOpen && (
                    <ImportModal
                        species={species}
                        vendors={vendors}
                        locations={locations}
                        onClose={() => setIsImportModalOpen(false)}
                        onSave={handleImportAndSave}
                    />
                )}
            </>
        </AppThemeProvider>
    );
};

export default App;
