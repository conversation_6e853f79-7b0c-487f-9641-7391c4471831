---
name: "OCR Document Import with Gemini AI"
description: "Intelligent document import system using Google's Gemini multimodal AI to extract structured data from handwritten and printed seafood business documents"
---

## Goal

**Feature Goal**: Enable users to photograph or upload paper documents (bills of lading, HACCP logs, inventory sheets) and automatically extract structured data into the system with 90%+ accuracy

**Deliverable**: OCRImportModal component with Gemini AI integration that processes documents and creates structured events/records in Firestore

**Success Definition**:
- User can import a bill of lading in under 5 minutes (vs. 20 minutes manual entry)
- 90%+ field extraction accuracy for printed documents
- 80%+ accuracy for handwritten documents
- User can review and correct extracted data before saving

## User Persona

**Target User**: Receiving manager, warehouse staff, compliance officer

**Use Case**:
1. **Primary**: Photograph bill of lading when seafood delivery arrives, auto-populate receiving event
2. **Secondary**: Digitize historical paper HACCP logs for compliance records
3. **Tertiary**: Bulk import daily temperature logs from handwritten sheets

**User Journey**:
1. User receives seafood delivery with paper BOL
2. Opens app, taps "Import Document" button
3. Takes photo of B<PERSON> with phone camera
4. AI processes image in 3-5 seconds
5. System displays extracted data for review
6. User corrects any errors (1-2 fields typically)
7. User saves → receiving events created automatically
8. Total time: 4 minutes vs. 20 minutes manual entry

**Pain Points Addressed**:
- Manual data entry is slow and tedious (15-30 min per document)
- High transcription error rate (10-15%) with manual entry
- Historical paper records difficult to search/analyze
- Receiving process bottleneck during busy delivery times

## Why

- **Time Savings**: Reduce document processing from 20 minutes to 4 minutes (80% reduction)
- **Accuracy Improvement**: AI extraction eliminates transcription errors, improves data quality
- **Compliance**: Easy digitization of paper logs for FDA/HACCP audits
- **Historical Value**: Enables digitization of years of paper records for analysis
- **Operational Efficiency**: Receiving staff can process deliveries faster, reducing dock time

## What

### User-Visible Behavior

1. **Import Modal**: New "Import Document" button in header launches OCR modal
2. **Capture Options**: Camera capture or file upload (JPG, PNG, PDF)
3. **Document Processing**: AI analyzes image, shows progress indicator
4. **Auto-Detection**: System identifies document type (BOL, temperature log, inventory sheet)
5. **Data Review**: Side-by-side view of document image and extracted data
6. **Inline Editing**: User can correct/modify any extracted field
7. **Confidence Indicators**: Visual indicators show field confidence (high/medium/low)
8. **Batch Save**: Extracted data saved as structured events in Firestore

### Success Criteria

- [ ] User can photograph document and trigger OCR processing
- [ ] Gemini API successfully extracts structured data from BOL, temperature logs, inventory sheets
- [ ] System auto-detects document type with 80%+ accuracy
- [ ] Data extraction accuracy: 90%+ for printed docs, 80%+ for handwritten
- [ ] User can review and edit all extracted fields
- [ ] Extracted data saves as proper Firestore events
- [ ] Processing completes in < 10 seconds per document
- [ ] Graceful error handling for low-quality images or API failures

## All Needed Context

### Context Completeness Check

_If someone knew nothing about this codebase, they would need: existing modal patterns, Firestore data structures, image upload utilities, Gemini API setup, and existing form validation patterns._

### Documentation & References

```yaml
- url: https://ai.google.dev/gemini-api/docs/vision
  why: Gemini multimodal capabilities for document OCR and structured extraction
  critical: Use gemini-2.5-flash for cost-effectiveness, proper prompt engineering for JSON extraction

- url: https://ai.google.dev/gemini-api/docs/document-processing
  why: Best practices for document processing with Gemini
  pattern: How to handle multi-page documents and low-quality images

- file: components/CameraModal.tsx
  why: Existing camera capture component - pattern for photo capture, image preview, Gemini integration
  pattern: Camera permissions, base64 encoding, image upload to Firebase Storage
  gotcha: Need to compress images before uploading to save bandwidth

- file: components/ImportModal.tsx
  why: Existing import modal - pattern for file upload, data validation
  pattern: Drag-and-drop file upload, CSV parsing, batch event creation
  gotcha: Must handle large files gracefully, validate before batch upload

- file: utils/imageUtils.ts
  why: Image handling utilities - base64 encoding, Firebase Storage upload
  pattern: uploadImage() function, blobToBase64() conversion

- file: App.tsx (lines 965-1031)
  why: Voice assistant species/vendor creation functions - pattern for fuzzy matching
  pattern: createSpecies(), createVendor() - shows data validation and Firestore writes

- file: types.ts (lines 3-40)
  why: HACCPEvent type definition - structure for all event types
  critical: Must create events matching this exact schema

- docfile: NONE (create new)
  why: Need to document Gemini prompt engineering patterns for this feature
  section: Create PRPs/ai_docs/GEMINI_OCR_PATTERNS.md
```

### Current Codebase Tree

```bash
Haccp-Helper/
├── App.tsx                      # Main app with voice assistant, event management
├── components/
│   ├── CameraModal.tsx          # Existing camera capture with Gemini analysis
│   ├── ImportModal.tsx          # Existing CSV import modal
│   ├── Dashboard.tsx            # Event display
│   └── IconComponents.tsx       # Icon library
├── utils/
│   ├── imageUtils.ts            # Image upload/encoding helpers
│   └── audioUtils.ts
├── types.ts                     # TypeScript interfaces (HACCPEvent, etc.)
├── firebase/
│   └── config.ts                # Firebase initialization
└── PRPs/                        # Planning documents
```

### Desired Codebase Tree (files to add)

```bash
Haccp-Helper/
├── components/
│   ├── OCRImportModal.tsx                    # NEW: Main OCR import modal component
│   ├── DocumentPreview.tsx                   # NEW: Zoomable document image viewer
│   ├── ExtractedDataEditor.tsx               # NEW: Form for reviewing/editing extracted data
│   └── DocumentTypeSelector.tsx              # NEW: Manual document type picker (fallback)
├── services/
│   └── geminiOCR.ts                          # NEW: Gemini API integration for OCR
├── utils/
│   ├── documentParsers.ts                    # NEW: Parse Gemini responses into typed data
│   └── imagePreprocessing.ts                 # NEW: Image compression/rotation before upload
├── types/
│   └── ocrTypes.ts                           # NEW: TypeScript types for OCR data structures
└── PRPs/
    └── ai_docs/
        └── GEMINI_OCR_PATTERNS.md            # NEW: Gemini prompt engineering guide
```

### Known Gotchas & Library Quirks

```typescript
// CRITICAL: Gemini API requires explicit JSON mode in prompt
// Bad: "Extract the data from this document"
// Good: "Return JSON with schema: { documentType: string, extractedData: {...} }"

// CRITICAL: Firebase Storage URLs expire after download
// Must upload images BEFORE sending to Gemini to get permanent URLs

// GOTCHA: React Camera API permissions must be requested in user gesture handler
// Cannot auto-request camera permission on page load - must be in button click

// GOTCHA: Gemini 2.5 Flash has 1MB image size limit
// Must compress images client-side before API call (use canvas.toBlob with quality=0.8)

// PATTERN: Existing modals use isOpen/onClose pattern for state management
// Follow same pattern for consistency: <OCRImportModal isOpen={...} onClose={...} />

// PATTERN: Firestore writes use serverTimestamp() for createdAt/updatedAt
// Don't use Date.now() - causes inconsistency with other events

// GOTCHA: Voice assistant already has species/vendor creation logic
// Don't duplicate - extract into shared utility or reference existing implementation
```

## Implementation Blueprint

### Data Models and Structure

```typescript
// types/ocrTypes.ts - Core OCR type definitions

export type DocumentType =
  | 'bill_of_lading'
  | 'temperature_log'
  | 'inventory_count'
  | 'delivery_receipt'
  | 'sales_order'
  | 'sanitation_log'
  | 'unknown';

export interface OCRResponse {
  documentType: DocumentType;
  confidence: number; // 0.0 - 1.0
  extractedData: BillOfLadingData | TemperatureLogData | InventoryCountData | Record<string, any>;
  warnings: string[];
  rawText: string;
}

export interface BillOfLadingData {
  bolNumber?: string;
  vendor: string;
  deliveryDate: string; // ISO 8601
  deliveryTime?: string;
  items: Array<{
    species: string;
    productForm?: string;
    quantity: number;
    unit: 'lbs' | 'kg';
    temperature?: number;
    temperatureUnit?: 'F' | 'C';
    origin?: string;
    harvestDate?: string;
    lotNumber?: string;
  }>;
  receiverName?: string;
  conditionNotes?: string;
}

export interface TemperatureLogData {
  logDate: string;
  location: string;
  readings: Array<{
    time: string;
    product?: string;
    temperature: number;
    temperatureUnit: 'F' | 'C';
    inspectorName?: string;
  }>;
}

export interface InventoryCountData {
  countDate: string;
  location: string;
  items: Array<{
    species: string;
    productForm?: string;
    quantity: number;
    unit: 'lbs' | 'kg';
  }>;
  counterName?: string;
}
```

### Implementation Tasks (ordered by dependencies)

#### Phase 1: Foundation (Week 1)

```yaml
Task 1: CREATE types/ocrTypes.ts
  - IMPLEMENT: TypeScript interfaces for OCRResponse, BillOfLadingData, TemperatureLogData, InventoryCountData
  - PATTERN: Follow types.ts structure for consistency
  - NAMING: Export interfaces with 'Data' suffix (BillOfLadingData not BillOfLading)
  - PLACEMENT: types/ directory alongside types.ts
  - VALIDATE: npm run typecheck && echo "✓ Types validated"

Task 2: CREATE PRPs/ai_docs/GEMINI_OCR_PATTERNS.md
  - IMPLEMENT: Document Gemini prompt engineering patterns for OCR
  - CONTENT:
    * System prompts for each document type
    * JSON schema examples
    * Common failure cases and fixes
    * Example responses
  - PURPOSE: Reference document for future prompt improvements
  - VALIDATE: cat PRPs/ai_docs/GEMINI_OCR_PATTERNS.md | wc -l (should be >50 lines)

Task 3: CREATE services/geminiOCR.ts
  - IMPLEMENT: analyzeDocument(imageFile: File, documentType?: DocumentType): Promise<OCRResponse>
  - PATTERN: Follow App.tsx handleImageAnalysis() (lines 401-434) for Gemini API structure
  - IMPORTS:
    * import { GoogleGenAI } from '@google/genai'
    * import type { OCRResponse, DocumentType } from '../types/ocrTypes'
    * import { blobToBase64 } from '../utils/imageUtils'
  - KEY LOGIC:
    * Compress image if > 1MB using canvas
    * Convert to base64 using blobToBase64()
    * Send to Gemini 2.5 Flash with structured prompt
    * Parse JSON response into OCRResponse type
    * Handle errors gracefully (network, API, parsing)
  - GOTCHA: Must wrap Gemini call in try-catch - API can fail/timeout
  - CRITICAL: Use gemini-2.5-flash model (not gemini-pro-vision) for cost
  - VALIDATE: npm run build && grep -q "analyzeDocument" dist/services/geminiOCR.js && echo "✓ Service compiled"
```

#### Phase 2: Core Components (Week 1-2)

```yaml
Task 4: CREATE utils/imagePreprocessing.ts
  - IMPLEMENT:
    * compressImage(file: File, maxSizeMB: number): Promise<File>
    * rotateImage(file: File, degrees: number): Promise<File>
  - PATTERN: Use browser canvas API for compression/rotation
  - GOTCHA: Must preserve EXIF orientation when rotating
  - VALIDATE: npm run build && echo "✓ Image utils compiled"

Task 5: CREATE utils/documentParsers.ts
  - IMPLEMENT: Functions to map OCRResponse to Firestore event structure
    * parseBillOfLading(data: BillOfLadingData): Partial<HACCPEvent>[]
    * parseTemperatureLog(data: TemperatureLogData): Partial<HACCPEvent>[]
    * parseInventoryCount(data: InventoryCountData): Partial<HACCPEvent>[]
  - PATTERN: Follow App.tsx logHACCPEvent (lines 777-789) for event structure
  - CRITICAL: Must match HACCPEvent interface from types.ts exactly
  - IMPORTS:
    * import type { HACCPEvent } from '../types'
    * import type { BillOfLadingData, TemperatureLogData, InventoryCountData } from '../types/ocrTypes'
  - GOTCHA: Date fields must be YYYY-MM-DD format, time must be HH:MM
  - VALIDATE: npm run typecheck && echo "✓ Parser types valid"

Task 6: CREATE components/DocumentPreview.tsx
  - IMPLEMENT: Zoomable image viewer component
  - PROPS:
    * imageUrl: string
    * onRotate?: (degrees: number) => void
  - FEATURES:
    * Display image in scrollable container
    * Zoom in/out buttons (1x, 1.5x, 2x)
    * Rotate buttons (90° increments)
    * Download original button
  - PATTERN: Follow existing modal layout patterns from CameraModal.tsx
  - UI: Use MUI Paper, IconButton, Box components for consistency
  - VALIDATE: npm run build && grep -q "DocumentPreview" dist/components/DocumentPreview.js && echo "✓ Component built"

Task 7: CREATE components/DocumentTypeSelector.tsx
  - IMPLEMENT: Dropdown for manual document type selection
  - PROPS:
    * documentType: DocumentType
    * onChange: (type: DocumentType) => void
    * confidence?: number
  - UI:
    * MUI Select component
    * Show confidence badge if provided
    * Options: Bill of Lading, Temperature Log, Inventory Count, etc.
  - PATTERN: Follow form Select components in App.tsx (lines 1331-1347)
  - VALIDATE: npm run dev (should render without errors)
```

#### Phase 3: Main Modal Component (Week 2)

```yaml
Task 8: CREATE components/ExtractedDataEditor.tsx
  - IMPLEMENT: Dynamic form for editing extracted data based on document type
  - PROPS:
    * documentType: DocumentType
    * data: OCRResponse['extractedData']
    * onChange: (data: any) => void
    * species: Species[]
    * vendors: Vendor[]
    * locations: Location[]
    * warnings: string[]
  - FEATURES:
    * Dynamic fields based on documentType
    * Autocomplete for species/vendors/locations (fuzzy match)
    * Date/time pickers
    * Confidence indicators per field
    * Add/remove items for multi-item documents (BOL)
    * Warning alerts at top
  - PATTERN: Follow event form in App.tsx (lines 1308-1680) for field structure
  - UI: MUI TextField, Autocomplete, DatePicker, Stack, Grid
  - CRITICAL: Must validate all fields before allowing save
  - GOTCHA: Species/vendor names may not match exactly - need fuzzy match
  - VALIDATE: npm run dev (should render form for each document type)

Task 9: CREATE components/OCRImportModal.tsx (Part 1: Structure)
  - IMPLEMENT: Modal shell with state management
  - PROPS:
    * isOpen: boolean
    * onClose: () => void
    * onSave: (events: Partial<HACCPEvent>[]) => Promise<void>
    * species: Species[]
    * vendors: Vendor[]
    * locations: Location[]
  - STATE:
    * step: 'capture' | 'processing' | 'review' | 'success'
    * capturedImage: File | null
    * imagePreview: string | null
    * ocrResponse: OCRResponse | null
    * editedData: any
    * isProcessing: boolean
    * error: string | null
  - PATTERN: Follow CameraModal.tsx structure (lines 1924-1932) for modal props
  - UI: MUI Dialog with maxWidth="lg" fullWidth
  - VALIDATE: npm run dev && console.log("Modal renders")

Task 10: CREATE components/OCRImportModal.tsx (Part 2: Capture Step)
  - IMPLEMENT: Camera capture and file upload UI
  - FEATURES:
    * Camera capture button (reuse logic from CameraModal.tsx)
    * File upload with drag-and-drop
    * Image preview after capture
    * Retake/choose different file option
  - PATTERN:
    * Camera: CameraModal.tsx handleCapture logic
    * Upload: Standard file input with onChange handler
  - UI:
    * Two large buttons: "Take Photo" and "Upload File"
    * Preview area showing captured/uploaded image
    * "Process Document" button (disabled until image selected)
  - GOTCHA: Camera permission must be requested in user gesture (button click)
  - VALIDATE: npm run dev && test camera capture + file upload

Task 11: CREATE components/OCRImportModal.tsx (Part 3: Processing Step)
  - IMPLEMENT: Call Gemini API and handle response
  - FLOW:
    1. Compress image using imagePreprocessing.ts
    2. Upload to Firebase Storage using uploadImage()
    3. Call geminiOCR.analyzeDocument()
    4. Parse response
    5. Set state: step='review', ocrResponse={...}
  - UI:
    * Loading spinner
    * "Analyzing document..." message
    * Progress indicator if possible
  - ERROR HANDLING:
    * Try/catch around all async operations
    * Show user-friendly error messages
    * Provide "Retry" button on failure
  - PATTERN: Follow App.tsx handleImageAnalysis (lines 401-434)
  - CRITICAL: Must handle Gemini API failures gracefully
  - VALIDATE: npm run dev && test with sample image

Task 12: CREATE components/OCRImportModal.tsx (Part 4: Review Step)
  - IMPLEMENT: Side-by-side document preview and data editor
  - LAYOUT:
    * Left: DocumentPreview component (40% width)
    * Right: ExtractedDataEditor component (60% width)
    * Top: DocumentTypeSelector (if confidence low, allow manual override)
  - ACTIONS:
    * "Cancel" button → close modal
    * "Save" button → parse data, call onSave callback
  - VALIDATION:
    * Must validate all required fields before save
    * Show inline errors for invalid fields
  - PATTERN: Two-column layout using MUI Grid
  - VALIDATE: npm run dev && test review/edit flow

Task 13: CREATE components/OCRImportModal.tsx (Part 5: Success Step)
  - IMPLEMENT: Confirmation UI after successful save
  - UI:
    * Success checkmark icon
    * "Imported X items from [document type]" message
    * List of items created (bullet points)
    * "Import Another" button → reset to capture step
    * "View Dashboard" button → close modal and navigate
  - PATTERN: Simple Stack layout with Typography and Buttons
  - VALIDATE: npm run dev && test full flow
```

#### Phase 4: Integration & Testing (Week 2-3)

```yaml
Task 14: MODIFY App.tsx (Add OCR Import Button)
  - FIND: Header button area with "New Event" button (around line 1802)
  - ADD: "Import Document" button next to "New Event"
  - IMPORT: import { OCRImportModal } from './components/OCRImportModal'
  - STATE: Add isOCRModalOpen state: const [isOCRModalOpen, setIsOCRModalOpen] = useState(false)
  - HANDLER: Create handleOCRSave to batch-create events
  - RENDER: Add <OCRImportModal isOpen={isOCRModalOpen} onClose={...} onSave={handleOCRSave} />
  - PATTERN: Follow existing modal pattern for EmailDraftModal (lines 1916-1922)
  - VALIDATE: npm run dev && click "Import Document" button (modal opens)

Task 15: IMPLEMENT App.tsx handleOCRSave function
  - LOCATION: After handleImportAndSave (line 1303)
  - SIGNATURE: const handleOCRSave = async (events: Partial<HACCPEvent>[]) => Promise<void>
  - LOGIC:
    1. Create Firestore batch
    2. For each event, add to batch with serverTimestamp()
    3. Check for new species/vendors/locations (like handleCaptureAndSave, lines 1261-1298)
    4. Commit batch
    5. Close modal and navigate to dashboard
  - PATTERN: Exactly follow handleCaptureAndSave logic
  - CRITICAL: Must create new species/vendors if they don't exist
  - VALIDATE: npm run dev && test full import flow (should create events in Firestore)

Task 16: CREATE services/__tests__/geminiOCR.test.ts
  - IMPLEMENT: Unit tests for Gemini OCR service
  - TEST CASES:
    * Valid bill of lading extraction
    * Valid temperature log extraction
    * Handles low-confidence responses
    * Handles API errors gracefully
    * Image compression works
  - PATTERN: Use Jest/Vitest with mocked Gemini API
  - MOCK: GoogleGenAI API responses
  - VALIDATE: npm run test services/geminiOCR.test.ts

Task 17: CREATE utils/__tests__/documentParsers.test.ts
  - IMPLEMENT: Unit tests for document parsers
  - TEST CASES:
    * BillOfLading → HACCPEvent[] conversion
    * TemperatureLog → HACCPEvent[] conversion
    * InventoryCount → HACCPEvent[] conversion
    * Handles missing optional fields
    * Validates date formats
  - PATTERN: Standard Jest/Vitest unit tests
  - VALIDATE: npm run test utils/documentParsers.test.ts

Task 18: MANUAL TESTING with real documents
  - COLLECT: 10 sample documents (bills of lading, temperature logs, inventory sheets)
  - TEST: Import each document and measure:
    * Accuracy: % of fields extracted correctly
    * Time: Seconds from capture to save
    * Errors: Any failures or crashes
  - DOCUMENT: Create test report in PRPs/testing/ocr-test-results.md
  - ITERATE: Refine prompts based on results
  - VALIDATE: 80%+ accuracy on all document types
```

#### Phase 5: Polish & Documentation (Week 3)

```yaml
Task 19: ENHANCE services/geminiOCR.ts with improved prompts
  - REFINE: System prompts based on testing results
  - ADD: Document-specific extraction hints
  - IMPROVE: Error messages and user guidance
  - DOCUMENT: Update PRPs/ai_docs/GEMINI_OCR_PATTERNS.md with learnings
  - VALIDATE: npm run test && manual test with problem documents

Task 20: ADD user feedback mechanism
  - IMPLEMENT: "Was this accurate?" feedback prompt after save
  - COLLECT: Track accuracy feedback in Firestore
  - PURPOSE: Identify prompt improvement opportunities
  - UI: Simple thumbs up/down after successful import
  - VALIDATE: npm run dev && test feedback flow

Task 21: OPTIMIZE image compression for API cost savings
  - IMPLEMENT: Smarter compression (target ~500KB per image)
  - MEASURE: Average API token usage per document
  - GOAL: Keep costs under $0.10 per 100 documents
  - VALIDATE: Test with large (5MB+) images → should compress to <1MB

Task 22: ADD error recovery and retry logic
  - IMPLEMENT: Automatic retry with exponential backoff (max 3 attempts)
  - IMPROVE: Error messages with actionable guidance
  - ADD: "Save image for later" option if API unavailable
  - VALIDATE: npm run dev && test with network disabled (should handle gracefully)
```

### Implementation Patterns & Key Details

```typescript
// PATTERN: Gemini OCR Service Structure
// services/geminiOCR.ts

import { GoogleGenAI } from '@google/genai';
import type { OCRResponse, DocumentType } from '../types/ocrTypes';
import { blobToBase64 } from '../utils/imageUtils';
import { compressImage } from '../utils/imagePreprocessing';

const MAX_RETRIES = 3;
const BASE_DELAY_MS = 500;
const MAX_JITTER_MS = 250;

const ai = new GoogleGenAI({ apiKey: import.meta.env.VITE_GEMINI_API_KEY });

export async function analyzeDocument(
  imageFile: File,
  documentType?: DocumentType,
  attempt = 1
): Promise<OCRResponse> {
  try {
    // STEP 1: Compress if needed
    const compressed = await compressImage(imageFile, 1); // 1MB max

    // STEP 2: Convert to base64
    const base64Data = await blobToBase64(compressed);

    // STEP 3: Build Gemini prompt
    const prompt = buildExtractionPrompt(documentType);

    // STEP 4: Call Gemini API
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: {
        parts: [
          { inlineData: { mimeType: compressed.type, data: base64Data } },
          { text: prompt }
        ]
      }
    });

    // STEP 5: Parse JSON response
    const jsonText = extractJSON(response.text);
    const parsed: OCRResponse = JSON.parse(jsonText);

    return parsed;
  } catch (error) {
    const err = error as Error & { status?: number; response?: { status?: number } };
    const isTransient = isTransientError(err);
    const shouldRetry = isTransient && attempt < MAX_RETRIES;

    if (shouldRetry) {
      const backoffDelay = getBackoffDelay(attempt);
      console.warn(
        `Gemini OCR attempt ${attempt} failed with ${err.message ?? 'unknown error'}; retrying in ${backoffDelay}ms`
      );
      await delay(backoffDelay);
      return analyzeDocument(imageFile, documentType, attempt + 1);
    }

    console.error('Gemini OCR failed; returning fallback response for manual handling.', err);
    return buildFallbackResponse(err, documentType, isTransient);
  }
}

// CRITICAL: Prompt must request JSON format explicitly
function buildExtractionPrompt(documentType?: DocumentType): string {
  return `
You are an expert at extracting structured data from seafood business documents.

Analyze this document and return ONLY valid JSON (no markdown, no explanations).

${documentType ? `This is a ${documentType} document.` : 'First, identify the document type.'}

Return this exact JSON structure:
{
  "documentType": "bill_of_lading" | "temperature_log" | "inventory_count" | "unknown",
  "confidence": 0.0-1.0,
  "extractedData": {
    // For bill_of_lading:
    "vendor": "string",
    "deliveryDate": "YYYY-MM-DD",
    "items": [
      { "species": "string", "quantity": number, "unit": "lbs", "temperature": number }
    ]

    // For temperature_log:
    "logDate": "YYYY-MM-DD",
    "location": "string",
    "readings": [
      { "time": "HH:MM", "product": "string", "temperature": number }
    ]

    // For inventory_count:
    "countDate": "YYYY-MM-DD",
    "location": "string",
    "items": [
      { "species": "string", "quantity": number, "unit": "lbs" }
    ]
  },
  "warnings": ["any unclear or missing fields"],
  "rawText": "full OCR text"
}

IMPORTANT:
- Use exact field names above
- Dates must be YYYY-MM-DD format
- Times must be HH:MM format
- Handle handwriting variations
- If a field is unclear, include in warnings array
`;
}

// GOTCHA: Gemini might wrap JSON in markdown code blocks
function extractJSON(text: string): string {
  // Remove markdown code blocks if present
  const jsonMatch = text.match(/```json\n([\s\S]*?)\n```/) || text.match(/```\n([\s\S]*?)\n```/);
  return jsonMatch ? jsonMatch[1] : text;
}

function isTransientError(error: { status?: number; response?: { status?: number }; message?: string }): boolean {
  const status = error.status ?? error.response?.status;
  const message = error.message?.toUpperCase() ?? '';
  if (status === 429) return true;
  if (message.includes('RESOURCE_EXHAUSTED') || message.includes('QUOTA') || message.includes('UNAVAILABLE')) {
    return true;
  }

  const transientCodes = ['ECONNRESET', 'ETIMEDOUT', 'EAI_AGAIN', 'ENOTFOUND', 'FETCH FAILED'];
  return transientCodes.some(code => message.includes(code));
}

function getBackoffDelay(attempt: number): number {
  const exponential = BASE_DELAY_MS * Math.pow(2, attempt - 1);
  const jitter = Math.floor(Math.random() * MAX_JITTER_MS);
  return exponential + jitter;
}

function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function buildFallbackResponse(
  error: Error | undefined,
  documentType: DocumentType = 'unknown',
  canQueueForLater = false
): OCRResponse {
  const message = error?.message ?? 'Unknown error';
  const manualInstruction =
    'Automated OCR is temporarily unavailable. Please enter this document manually or save it for later processing.';

  return {
    documentType,
    confidence: 0,
    extractedData: {
      fallbackAction: canQueueForLater ? 'queued_for_retry' : 'manual_entry_required',
      saveImageForLater: true,
      queuedForRetry: canQueueForLater,
      allowManualEntry: true,
      instructions: manualInstruction,
      lastErrorMessage: message
    },
    warnings: [
      manualInstruction,
      `Last OCR engine error: ${message}`
    ],
    rawText: ''
  };
}
```

```typescript
// PATTERN: Document Parser Structure
// utils/documentParsers.ts

import type { HACCPEvent } from '../types';
import type { BillOfLadingData, TemperatureLogData } from '../types/ocrTypes';
import { serverTimestamp } from 'firebase/firestore';

export function parseBillOfLading(
  data: BillOfLadingData,
  currentUser: string
): Partial<HACCPEvent>[] {
  // Map each item to a receiving event
  return data.items.map(item => ({
    eventType: 'receiving',
    date: data.deliveryDate,
    time: data.deliveryTime || new Date().toTimeString().substring(0, 5),
    product: item.species,
    productForm: item.productForm,
    quantity: item.quantity,
    unit: 'lbs' as const,
    temperature: item.temperature,
    supplier: data.vendor,
    batchNumber: item.lotNumber,
    origin: item.origin,
    notes: data.conditionNotes,
    createdBy: currentUser,
    createdAt: serverTimestamp(),
  }));
}

// GOTCHA: Temperature logs create multiple events
export function parseTemperatureLog(
  data: TemperatureLogData,
  currentUser: string
): Partial<HACCPEvent>[] {
  return data.readings.map(reading => ({
    eventType: 'thermometer-calibration', // Or custom type
    date: data.logDate,
    time: reading.time,
    location: data.location,
    product: reading.product,
    temperature: reading.temperature,
    notes: `Temperature check by ${reading.inspectorName}`,
    createdBy: currentUser,
    createdAt: serverTimestamp(),
  }));
}
```

```typescript
// PATTERN: OCRImportModal State Management
// components/OCRImportModal.tsx

export const OCRImportModal: React.FC<OCRImportModalProps> = ({
  isOpen,
  onClose,
  onSave,
  species,
  vendors,
  locations,
}) => {
  const [step, setStep] = useState<'capture' | 'processing' | 'review' | 'success'>('capture');
  const [capturedImage, setCapturedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [ocrResponse, setOcrResponse] = useState<OCRResponse | null>(null);
  const [editedData, setEditedData] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle file selection (camera or upload)
  const handleImageSelect = (file: File) => {
    setCapturedImage(file);
    const previewUrl = URL.createObjectURL(file);
    setImagePreview(previewUrl);
  };

  // Process document with Gemini
  const handleProcessDocument = async () => {
    if (!capturedImage) return;

    setIsProcessing(true);
    setError(null);
    setStep('processing');

    try {
      // Upload to Firebase Storage first (for audit trail)
      const imageUrl = await uploadImage(capturedImage, `ocr/${Date.now()}_${capturedImage.name}`);

      // Call Gemini OCR
      const response = await analyzeDocument(capturedImage);

      setOcrResponse(response);
      setEditedData(response.extractedData);
      setStep('review');
    } catch (err: any) {
      setError(err.message || 'Failed to process document. Please try again.');
      setStep('capture'); // Allow retry
    } finally {
      setIsProcessing(false);
    }
  };

  // Save extracted data
  const handleSave = async () => {
    if (!ocrResponse || !editedData) return;

    try {
      // Parse into HACCPEvents based on document type
      let events: Partial<HACCPEvent>[] = [];

      if (ocrResponse.documentType === 'bill_of_lading') {
        events = parseBillOfLading(editedData, currentUser);
      } else if (ocrResponse.documentType === 'temperature_log') {
        events = parseTemperatureLog(editedData, currentUser);
      }
      // ... other document types

      await onSave(events);
      setStep('success');
    } catch (err: any) {
      setError(err.message || 'Failed to save events.');
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="lg" fullWidth>
      {step === 'capture' && <CaptureStep onSelect={handleImageSelect} onProcess={handleProcessDocument} />}
      {step === 'processing' && <ProcessingStep />}
      {step === 'review' && <ReviewStep ocrResponse={ocrResponse} editedData={editedData} onChange={setEditedData} onSave={handleSave} />}
      {step === 'success' && <SuccessStep onClose={onClose} />}
    </Dialog>
  );
};
```

### Integration Points

```yaml
APP.TSX:
  - add state: isOCRModalOpen
  - add button: "Import Document" in header (line ~1802)
  - add handler: handleOCRSave(events: Partial<HACCPEvent>[])
  - add render: <OCRImportModal isOpen={...} onClose={...} onSave={handleOCRSave} />

FIREBASE:
  - no schema changes needed (uses existing events collection)
  - storage bucket: uses existing firebase storage for image uploads

ENVIRONMENT:
  - uses existing VITE_GEMINI_API_KEY from .env
  - no new env vars needed
```

## Validation Loop

### Level 1: Syntax & Style (Immediate Feedback)

```bash
# After creating each file
npm run typecheck                    # TypeScript validation
npm run lint                         # ESLint validation
npm run format                       # Prettier formatting

# Expected: Zero errors
```

### Level 2: Unit Tests (Component Validation)

```bash
# Test individual services/utilities
npm run test services/geminiOCR.test.ts
npm run test utils/documentParsers.test.ts
npm run test utils/imagePreprocessing.test.ts

# Test components (if tests written)
npm run test components/OCRImportModal.test.tsx

# Expected: All tests pass
```

### Level 3: Integration Testing (System Validation)

```bash
# Start dev server
npm run dev

# Manual testing checklist:
# 1. Click "Import Document" button → modal opens
# 2. Take photo with camera → image preview shows
# 3. Click "Process Document" → loading indicator appears
# 4. Wait 3-5 seconds → review step shows extracted data
# 5. Edit a field → changes reflect in state
# 6. Click "Save" → events created in Firestore
# 7. Check Firestore console → new events exist
# 8. Check Firebase Storage → uploaded images exist

# Test error cases:
# 9. Upload blurry image → warning appears, low confidence
# 10. Disconnect internet → error message shows with retry option
# 11. Upload non-document image → "unknown" type detected

# Expected: All manual tests pass, no console errors
```

### Level 4: Real-World Document Testing

```bash
# Collect real documents from seafood business
# Test with 10-20 real bills of lading, temperature logs, inventory sheets

# For each document:
# 1. Import via OCR
# 2. Measure accuracy (% of fields correct)
# 3. Measure time (seconds from capture to save)
# 4. Note any errors or failures

# Create test report:
# PRPs/testing/ocr-test-results.md

# Success criteria:
# - 90%+ accuracy on printed documents
# - 80%+ accuracy on handwritten documents
# - < 10 seconds processing time
# - < 5 minutes total user time (vs. 20 minutes manual)
```

## Final Validation Checklist

### Technical Validation

- [ ] All TypeScript files compile: `npm run build`
- [ ] No linting errors: `npm run lint`
- [ ] No type errors: `npm run typecheck`
- [ ] All unit tests pass: `npm run test`
- [ ] Components render without errors: `npm run dev`

### Feature Validation

- [ ] Can photograph document and trigger OCR
- [ ] Gemini API successfully extracts data
- [ ] Document type auto-detected (80%+ accuracy)
- [ ] Extracted data displayed for review
- [ ] User can edit all fields
- [ ] Saving creates proper Firestore events
- [ ] Works with bills of lading
- [ ] Works with temperature logs
- [ ] Works with inventory sheets
- [ ] Handles errors gracefully

### User Experience Validation

- [ ] Modal opens smoothly from header button
- [ ] Camera capture works on mobile devices
- [ ] File upload works with drag-and-drop
- [ ] Loading indicators show during processing
- [ ] Confidence indicators help identify uncertain fields
- [ ] Warning messages clear and actionable
- [ ] Success confirmation provides useful feedback
- [ ] Total import time < 5 minutes

### Code Quality Validation

- [ ] Follows existing modal patterns (CameraModal, ImportModal)
- [ ] Uses existing utilities (imageUtils, firestoreUtils)
- [ ] Consistent naming conventions
- [ ] Proper TypeScript types throughout
- [ ] Error handling in all async operations
- [ ] No hardcoded values (uses env vars)

### Real-World Validation

- [ ] Tested with 10+ real documents
- [ ] 90%+ accuracy on printed documents
- [ ] 80%+ accuracy on handwritten documents
- [ ] Processing time < 10 seconds
- [ ] API costs < $0.10 per 100 documents
- [ ] User feedback positive (4+ stars)

---

## Anti-Patterns to Avoid

- ❌ Don't send images directly to Gemini without compression (costs + performance)
- ❌ Don't skip image upload to Firebase Storage (need audit trail)
- ❌ Don't hardcode prompts in component code (keep in service layer)
- ❌ Don't auto-save without user review (accuracy not 100%)
- ❌ Don't duplicate species/vendor creation logic (reuse from App.tsx)
- ❌ Don't use synchronous file reading (browser APIs are async)
- ❌ Don't ignore Gemini confidence scores (use to guide UX)
- ❌ Don't create new modal patterns (follow existing CameraModal/ImportModal)
- ❌ Don't skip error boundaries (Gemini API can fail)
- ❌ Don't forget serverTimestamp() on Firestore writes (consistency)

---

## Cost & Performance Estimates

**Gemini API Costs**:
- Model: gemini-2.5-flash
- Cost: ~$0.075 per 1,000 images
- Expected volume: 100 docs/month = ~$0.0075/month
- Annual cost: ~$0.09/year (negligible)

**Firebase Storage**:
- Storage: $0.026/GB/month
- Expected volume: 100 images × 500KB = 50MB/month
- Annual cost: ~$0.15/year

**Performance Targets**:
- Image compression: < 1 second
- Gemini API call: 3-5 seconds
- Data parsing: < 0.5 seconds
- Firestore write: < 1 second
- **Total processing: < 10 seconds**

---

## Future Enhancements (Post-MVP)

1. **Batch Processing**: Upload multiple documents at once
2. **Smart Templates**: Learn from user corrections to improve prompts
3. **Offline Mode**: Queue documents for processing when connection restored
4. **Multi-Language**: Support Spanish/other languages
5. **Signature Extraction**: Extract and store signature images
6. **Barcode Scanning**: Read QR codes on documents
7. **Computer Vision**: Detect product condition from images
8. **Analytics Dashboard**: Track accuracy trends over time

---

**Document Version**: 1.0
**Last Updated**: 2025-01-06
**Status**: Ready for Implementation
