# Security Migration: Moving AI Processing to Server-Side

## Overview

This document outlines the security improvements made to the HACCP Helper application by moving AI processing from client-side to secure server-side Firebase Functions.

## Problem

The original `ImportModal.tsx` component was instantiating `GoogleGenAI` with `process.env.API_KEY` directly in client-side code, which:

1. **Exposed the API key** - Environment variables in client-side code are visible to users
2. **Security risk** - API keys could be extracted from the bundled JavaScript
3. **Browser compatibility** - `process.env` may not work reliably in all browsers
4. **No rate limiting** - Unlimited API calls could be made by malicious users
5. **No validation** - No server-side input validation or sanitization

## Solution

### 1. Created Firebase Cloud Functions

**New Files:**
- `functions/src/index.ts` - Main function implementation
- `functions/package.json` - Function dependencies
- `functions/tsconfig.json` - TypeScript configuration
- `functions/README.md` - Documentation and setup instructions
- `functions/.env.example` - Environment variable template

**Key Features:**
- Secure server-side API key storage using Firebase Secret Manager
- Rate limiting (10 requests per minute per IP address)
- Request size validation (max 1MB)
- Row count limits (max 100 rows to prevent token limit issues)
- Comprehensive input validation and sanitization
- Proper error handling with appropriate HTTP status codes
- CORS configuration for cross-origin requests

### 2. Updated Client-Side Code

**Modified Files:**
- `components/ImportModal.tsx` - Replaced direct AI calls with HTTP requests to Firebase Function
- `firebase.json` - Added functions configuration

**Changes Made:**
- Removed `GoogleGenAI` import and direct API instantiation
- Replaced AI processing with secure HTTP POST to Firebase Function
- Updated error handling to match new API response format
- Changed user messaging from "processed locally" to "processed securely"
- Improved error messages for rate limiting and server errors

### 3. Enhanced Security Features

**Rate Limiting:**
- 10 requests per minute per IP address
- Configurable time windows and limits
- Automatic cleanup of expired rate limit entries

**Input Validation:**
- Validates request structure and data types
- Enforces maximum row counts (100 rows)
- Checks request size limits (1MB maximum)
- Sanitizes all input parameters

**Error Handling:**
- Specific error messages for different failure scenarios
- No exposure of internal system details
- Proper HTTP status codes (400, 429, 500, etc.)
- Comprehensive logging for monitoring

## API Endpoint

**URL:** `https://us-central1-seafood-inventory-voice.cloudfunctions.net/parseSpreadsheet`

**Method:** POST

**Request Body:**
```json
{
  "spreadsheetData": [...],
  "headers": "column1, column2, ...",
  "species": ["species1", "species2", ...],
  "vendors": ["vendor1", "vendor2", ...],
  "locations": ["location1", "location2", ...]
}
```

**Response:**
```json
{
  "success": true,
  "events": [...],
  "processedRows": 50,
  "totalRows": 50
}
```

## Deployment

### Prerequisites
1. Firebase CLI installed: `npm install -g firebase-tools`
2. Firebase project configured
3. Google Gemini API key

### Setup Steps
1. **Install function dependencies:**
   ```bash
   cd functions
   npm install
   ```

2. **Configure API key:**
   ```bash
   echo "your_api_key_here" | firebase functions:secrets:set GEMINI_API_KEY
   ```

3. **Deploy functions:**
   ```bash
   firebase deploy --only functions
   ```

### Automated Deployment
Use the provided deployment script:
```bash
./deploy-functions.sh
```

## Security Benefits

1. **API Key Protection** - Gemini API key is stored securely in Firebase Secret Manager, never exposed to clients
2. **Rate Limiting** - Prevents abuse and controls API usage costs
3. **Input Validation** - Server-side validation prevents malicious or malformed requests
4. **Size Limits** - Prevents large requests that could cause timeouts or excessive costs
5. **Error Handling** - Proper error responses without exposing system internals
6. **Monitoring** - Centralized logging and monitoring through Firebase Console
7. **CORS Security** - Controlled cross-origin access

## Monitoring

Monitor the functions through Firebase Console:
- **Logs:** Firebase Console > Functions > Logs
- **Performance:** Firebase Console > Functions > Usage
- **Errors:** Firebase Console > Functions > Health

## Future Improvements

1. **Redis Rate Limiting** - Replace in-memory rate limiting with Redis for production scale
2. **Authentication** - Add user authentication to track usage per user
3. **Caching** - Implement response caching for identical requests
4. **Metrics** - Add detailed metrics and alerting
5. **Load Balancing** - Scale functions based on demand

## Testing

The function can be tested using curl:
```bash
curl -X POST \
  https://us-central1-seafood-inventory-voice.cloudfunctions.net/parseSpreadsheet \
  -H "Content-Type: application/json" \
  -d '{
    "spreadsheetData": [...],
    "headers": "Product,Quantity,Date",
    "species": ["Salmon", "Tuna"],
    "vendors": ["Vendor A"],
    "locations": ["Freezer 1"]
  }'
```

## Backward Compatibility

The client-side interface remains the same - users will not notice any difference in functionality, only improved security and reliability.
