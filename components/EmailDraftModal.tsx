import React, { useState, useEffect } from 'react';
import { GoogleGenAI } from '@google/genai';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import { EmailContext } from '../types';
import CloseIcon from '@mui/icons-material/Close';

interface EmailDraftModalProps {
  isOpen: boolean;
  onClose: () => void;
  context: EmailContext;
}

const getPromptForContext = (context: EmailContext): string => {
  switch (context.type) {
    case 'order': {
      const { order, vendor } = context;
      return `
        Generate a professional and concise purchase order email.
        - To: ${vendor.email || 'Vendor'}
        - From: The purchasing manager.
        - Subject: Purchase Order - ${order.species}
        - Body: Clearly state that we would like to place an order for ${order.quantity} lbs of ${order.species}.
        - Mention the desired delivery date is on or before ${order.expectedDeliveryDate}.
        - Ask them to confirm receipt of this PO and the expected delivery date.
        - Keep it friendly and professional. Do not use placeholders like [Your Name].
      `;
    }
    case 'receiving': {
      const { event } = context;
      const product = [event.product, event.productForm].filter(Boolean).join(' ');
      return `
        Generate a professional and concise internal memo for the warehouse team about an upcoming delivery.
        - To: Warehouse Team
        - From: Management
        - Subject: Incoming Delivery Notification: ${product} from ${event.supplier}
        - Body: Inform the team to expect a delivery of ${event.quantity} lbs of ${product} from ${event.supplier} on ${event.date}.
        - Specify that the product should be stored at location: ${event.location}.
        - Remind them to perform standard quality and temperature checks upon arrival (target temp: < 40°F if fresh, ~32°F if previously frozen).
        - Keep it clear and direct.
      `;
    }
  }
};

export const EmailDraftModal: React.FC<EmailDraftModalProps> = ({ isOpen, onClose, context }) => {
  const [draft, setDraft] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen && context) {
      const generateDraft = async () => {
        setIsLoading(true);
        setError('');
        setDraft('');
        try {
          const apiKey = import.meta.env.VITE_GEMINI_API_KEY as string | undefined;
          if (!apiKey) {
            throw new Error('Missing VITE_GEMINI_API_KEY');
          }
          const ai = new GoogleGenAI({ apiKey });
          const prompt = getPromptForContext(context);
          const response = await ai.models.generateContent({
            model: 'gemini-2.5-flash',
            contents: prompt,
          });
          setDraft(response.text);
        } catch (err: any) {
          console.error('Error generating email draft:', err);
          if (err.message?.includes('does not have permission') || err.message?.includes('not found')) {
            setError('API Key Error. Please close this window and re-select your API key from the main screen.');
          } else {
            setError("Sorry, I couldn't generate the email draft. Please try again.");
          }
        } finally {
          setIsLoading(false);
        }
      };
      generateDraft();
    }
  }, [isOpen, context]);

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(draft);
    alert('Email draft copied to clipboard!');
  };

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: { variant: 'elevation', elevation: 0 },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        AI-Generated Email Draft
        <IconButton onClick={onClose} edge="end" aria-label="Close email draft dialog">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        dividers
        sx={{ maxHeight: '60vh', overflowY: 'auto', overflowX: 'hidden', p: 3 }}
      >
        <Stack spacing={2}>
          {isLoading && (
            <Stack direction="row" spacing={1.5} alignItems="center" justifyContent="center">
              <CircularProgress size={20} />
              <span>Generating draft...</span>
            </Stack>
          )}
          {error && <Alert severity="error">{error}</Alert>}
          {!isLoading && !error && (
            <TextField
              multiline
              minRows={12}
              value={draft}
              onChange={(e) => setDraft(e.target.value)}
              InputProps={{ sx: { fontFamily: 'var(--font-family-mono, monospace)' } }}
            />
          )}
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCopyToClipboard} disabled={isLoading || !!error} variant="contained">
          Copy to Clipboard
        </Button>
      </DialogActions>
    </Dialog>
  );
};
