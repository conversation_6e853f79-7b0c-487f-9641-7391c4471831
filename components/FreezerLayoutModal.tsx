

import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Typography } from '@mui/material';
import { Location } from '../types';
import { CubeIcon } from './IconComponents';

interface FreezerLayoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  location: Location;
  inventory: { [product: string]: number };
}

export const FreezerLayoutModal: React.FC<FreezerLayoutModalProps> = ({ isOpen, onClose, location, inventory }) => {
  const inventoryItems = Object.entries(inventory).filter(([, qty]) => (qty as number) > 0.01);

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      slotProps={{
        paper: { variant: 'elevation', elevation: 0 },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        Layout for: {location.name}
        <IconButton onClick={onClose} edge="end" aria-label="Close freezer layout dialog">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        dividers
        sx={{ p: 3, maxHeight: '70vh', overflowY: 'auto', overflowX: 'hidden' }}
      >
        {location.description && (
          <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
            {location.description}
          </Typography>
        )}
        {inventoryItems.length > 0 ? (
          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: { xs: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)', lg: 'repeat(4, 1fr)' },
            gap: 2 
          }}>
            {inventoryItems.map(([product, quantity]) => (
              <Box 
                key={product} 
                sx={{ 
                  bgcolor: 'background.surface', 
                  border: 1, 
                  borderColor: 'divider', 
                  borderRadius: 2, 
                  p: 2, 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center', 
                  justifyContent: 'center', 
                  textAlign: 'center', 
                  height: 160 
                }}
              >
                <Box sx={{ color: 'primary.main', mb: 1, flexShrink: 0 }}>
                  <Box component={CubeIcon} sx={{ width: 40, height: 40 }} />
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', flexGrow: 1 }}>
                  <Typography variant="subtitle2" sx={{ color: 'text.primary', fontWeight: 600, wordBreak: 'break-word' }}>
                    {product}
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
                    {(quantity as number).toFixed(2)} lbs
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        ) : (
          <Box sx={{ textAlign: 'center', bgcolor: 'background.surface', p: 6, borderRadius: 2 }}>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              This location is currently empty.
            </Typography>
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};
