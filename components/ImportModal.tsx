import React, { useState, useCallback, useMemo, useEffect } from 'react';
import * as xlsx from 'xlsx';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Typography, Stack, Paper, TextField, Chip, Alert, CircularProgress, FormControl, InputLabel, Select, MenuItem, useTheme } from '@mui/material';
import { alpha } from '@mui/material/styles';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import DeleteIcon from '@mui/icons-material/Delete';
import { HACCPEvent, Species, Vendor, Location, EventType } from '../types';
import { TrashIcon } from './IconComponents';

interface ImportModalProps {
    species: Species[];
    vendors: Vendor[];
    locations: Location[];
    onClose: () => void;
    onSave: (events: Partial<HACCPEvent>[]) => void;
}

type ModalStep = 'upload' | 'loading' | 'confirm' | 'error';

type TempHACCPEvent = Partial<HACCPEvent> & { tempId: number };

// Maximum number of rows to process to avoid token limits
const MAX_ROWS = 100;
const PARSE_SPREADSHEET_URL = import.meta.env.VITE_PARSE_SPREADSHEET_URL;
const DEFAULT_REQUEST_TIMEOUT_MS = 30_000;

const fetchWithTimeout = async (input: RequestInfo, init: RequestInit = {}, timeoutMs: number = DEFAULT_REQUEST_TIMEOUT_MS) => {
    const controller = new AbortController();
    const timeoutId = window.setTimeout(() => controller.abort(), timeoutMs);

    try {
        const response = await fetch(input, { ...init, signal: controller.signal });
        return response;
    } catch (error: unknown) {
        if (error instanceof DOMException && error.name === 'AbortError') {
            throw new Error(`Request timed out after ${timeoutMs / 1000} seconds`);
        }
        throw error;
    } finally {
        window.clearTimeout(timeoutId);
    }
};

export const ImportModal: React.FC<ImportModalProps> = ({ species, vendors, locations, onClose, onSave }) => {
    const [step, setStep] = useState<ModalStep>('upload');
    const [originalEvents, setOriginalEvents] = useState<TempHACCPEvent[]>([]);
    const [extractedEvents, setExtractedEvents] = useState<TempHACCPEvent[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [overrideEventType, setOverrideEventType] = useState<EventType | 'auto'>('auto');

    const handleFile = useCallback(async (file: File) => {
        setStep('loading');
        setError(null);
        setOverrideEventType('auto');
        try {
            const data = await file.arrayBuffer();
            const workbook = xlsx.read(data);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const json: any[] = xlsx.utils.sheet_to_json(worksheet);

            if (json.length === 0) {
                setError("The uploaded file is empty or in an unsupported format.");
                setStep('error');
                return;
            }

            // Validate row count and enforce MAX_ROWS limit
            if (json.length > MAX_ROWS) {
                setError(`The uploaded file contains ${json.length} rows, which exceeds the maximum limit of ${MAX_ROWS} rows. Please split your data into smaller files or reduce the number of rows to avoid performance issues and token limits.`);
                setStep('error');
                return;
            }

            const headers = Object.keys(json[0]).join(', ');
            // Only process the limited dataset (should be <= MAX_ROWS at this point)
            const limitedData = json.slice(0, MAX_ROWS);

            if (!PARSE_SPREADSHEET_URL) {
                console.error('Missing VITE_PARSE_SPREADSHEET_URL configuration.');
                setError('Import configuration error: unable to reach the parsing service. Please contact support.');
                setStep('error');
                return;
            }

            // Call the secure Firebase Function instead of client-side AI
            const response = await fetchWithTimeout(PARSE_SPREADSHEET_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    spreadsheetData: limitedData,
                    headers: headers,
                    species: species.map(s => s.name),
                    vendors: vendors.map(v => v.name),
                    locations: locations.map(l => l.name),
                }),
            });

            const responseText = await response.text();

            if (!response.ok) {
                let errorMessage = `Server error: ${response.status}`;
                try {
                    const errorData = JSON.parse(responseText);
                    if (errorData?.error) {
                        errorMessage = errorData.error;
                    }
                } catch (parseError) {
                    console.error('Failed to parse error response from AI service:', parseError, responseText);
                }
                throw new Error(errorMessage);
            }

            let result: { success?: boolean; events?: Partial<HACCPEvent>[] } | null = null;
            try {
                result = JSON.parse(responseText);
            } catch (parseError) {
                console.error('AI service returned invalid JSON:', parseError, responseText);
                setError('AI analysis returned malformed data. Please try again later.');
                setStep('error');
                return;
            }

            if (!result || !result.success || !Array.isArray(result.events)) {
                throw new Error('Invalid response from server');
            }

            const parsedEvents = result.events.map((event: Partial<HACCPEvent>, index: number) => ({
                ...event,
                tempId: index,
            }));
            setOriginalEvents(parsedEvents);
            setExtractedEvents(parsedEvents);
            setStep('confirm');

        } catch (err: any) {
            console.error("Error processing file:", err);
            if (err.message?.includes('timed out')) {
                setError('The parsing service is taking too long to respond. Please try again in a few moments.');
            } else if (err.message?.includes('Failed to fetch') || err.message?.includes('NetworkError')) {
                setError('Unable to connect to the parsing service. Please check your network connection and try again.');
            } else if (err.message?.includes('Rate limit exceeded')) {
                setError("Too many requests. Please wait a moment and try again.");
            } else if (err.message?.includes('Server error: 429')) {
                setError("AI service is temporarily overloaded. Please try again in a few minutes.");
            } else if (err.message?.includes('Server error: 500')) {
                setError("AI service is temporarily unavailable. Please try again later.");
            } else if (err.message?.includes('authentication failed')) {
                setError("AI service configuration error. Please contact support.");
            } else {
                setError("AI analysis failed. Please ensure the file is a standard CSV or Excel format and try again.");
            }
            setStep('error');
        }
    }, [species, vendors, locations]);
    
    useEffect(() => {
        if (step !== 'confirm' || originalEvents.length === 0) return;

        if (overrideEventType === 'auto') {
            setExtractedEvents(currentEvents =>
                currentEvents.map(event => {
                    const originalEvent = originalEvents.find(o => o.tempId === event.tempId);
                    return {
                        ...event,
                        eventType: originalEvent ? originalEvent.eventType : 'inventory',
                    };
                })
            );
        } else {
            setExtractedEvents(currentEvents =>
                currentEvents.map(event => ({
                    ...event,
                    eventType: overrideEventType,
                }))
            );
        }
    }, [overrideEventType, originalEvents, step]);

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            handleFile(e.target.files[0]);
        }
    };
    
    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFile(e.dataTransfer.files[0]);
        }
    };

    const handleDataChange = (id: number, field: keyof HACCPEvent, value: string | number | null | undefined) => {
        setExtractedEvents(currentEvents =>
            currentEvents.map(event => {
                if (event.tempId !== id) return event;

                let nextValue: string | number | undefined;

                if (field === 'quantity') {
                    if (value === null || value === undefined) {
                        nextValue = undefined;
                    } else if (typeof value === 'string') {
                        const trimmedValue = value.trim();
                        if (trimmedValue === '') {
                            nextValue = undefined;
                        } else {
                            const parsedValue = Number(trimmedValue);
                            nextValue = Number.isFinite(parsedValue) ? parsedValue : undefined;
                        }
                    } else {
                        nextValue = Number.isFinite(value) ? value : undefined;
                    }
                } else {
                    nextValue = (value === null || value === undefined) ? undefined : (value as string | number);
                }

                return { ...event, [field]: nextValue };
            })
        );
    };

    const removeEvent = (id: number) => {
        setExtractedEvents(prev => prev.filter(event => event.tempId !== id));
        setOriginalEvents(prev => prev.filter(event => event.tempId !== id));
    };

    const { newSpecies, newVendors, newLocations } = useMemo(() => {
        const existingSpecies = new Set(species.map(s => s.name.toLowerCase()));
        const existingVendors = new Set(vendors.map(v => v.name.toLowerCase()));
        const existingLocations = new Set(locations.map(l => l.name.toLowerCase()));
        const newSpecies = new Set<string>();
        const newVendors = new Set<string>();
        const newLocations = new Set<string>();
        extractedEvents.forEach(event => {
            if (event.product && !existingSpecies.has(event.product.toLowerCase())) newSpecies.add(event.product);
            if (event.supplier && !existingVendors.has(event.supplier.toLowerCase())) newVendors.add(event.supplier);
            if (event.location && !existingLocations.has(event.location.toLowerCase())) newLocations.add(event.location);
        });
        return { newSpecies: [...newSpecies], newVendors: [...newVendors], newLocations: [...newLocations] };
    }, [extractedEvents, species, vendors, locations]);

    const theme = useTheme();
    
    const renderContent = () => {
        switch (step) {
            case 'upload':
                return (
                    <Box sx={{ p: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', textAlign: 'center', height: '100%' }}>
                        <Paper
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            variant="outlined"
                            sx={{
                                width: '100%',
                                p: 5,
                                border: '2px dashed',
                                borderColor: isDragging ? 'primary.main' : 'divider',
                                bgcolor: isDragging ? (t) => alpha(t.palette.primary.main, 0.08) : 'transparent',
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                transition: 'all 0.2s',
                            }}
                        >
                            <UploadFileIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                                Drag & drop your CSV or Excel file here
                            </Typography>
                            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                                or
                            </Typography>
                            <Button variant="contained" component="label">
                                Browse Files
                                <input id="file-upload" type="file" hidden onChange={handleFileSelect} accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
                            </Button>
                        </Paper>
                        <Typography variant="caption" sx={{ color: 'text.secondary', mt: 2 }}>
                            Your data is processed securely on our servers.
                        </Typography>
                    </Box>
                );
            case 'loading':
                return (
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                        <CircularProgress size={64} />
                        <Typography variant="h6" sx={{ mt: 2, color: 'text.primary' }}>
                            Parsing file & analyzing data...
                        </Typography>
                    </Box>
                );
            case 'error':
                 return (
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%', textAlign: 'center', p: 3 }}>
                        <Alert severity="error" sx={{ mb: 3, maxWidth: 600 }}>
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>An Error Occurred</Typography>
                            <Typography variant="body2" sx={{ mt: 1 }}>{error}</Typography>
                        </Alert>
                        <Button onClick={() => setStep('upload')} variant="contained">
                            Try Again
                        </Button>
                    </Box>
                );
            case 'confirm':
                const hasNewItems = newSpecies.length > 0 || newVendors.length > 0 || newLocations.length > 0;
                return (
                    <Box sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
                        <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 2, color: 'text.primary' }}>
                            Confirm Imported Data
                        </Typography>
                        {extractedEvents.length === MAX_ROWS && (
                            <Alert severity="warning" sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>Row limit reached</Typography>
                                <Typography variant="body2">
                                    Only the first {MAX_ROWS} rows were processed to ensure optimal performance and avoid token limits.
                                    If you need to import more data, please split your file into smaller batches.
                                </Typography>
                            </Alert>
                        )}
                        <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                            <FormControl fullWidth size="small">
                                <InputLabel id="eventTypeOverride-label">Set all events to type</InputLabel>
                                <Select
                                    labelId="eventTypeOverride-label"
                                    id="eventTypeOverride"
                                    value={overrideEventType}
                                    label="Set all events to type"
                                    onChange={e => setOverrideEventType(e.target.value as EventType | 'auto')}
                                >
                                    <MenuItem value="auto">Auto-Detect (from file)</MenuItem>
                                    <MenuItem value="receiving">Receiving</MenuItem>
                                    <MenuItem value="sales">Sales</MenuItem>
                                    <MenuItem value="disposal">Disposal</MenuItem>
                                    <MenuItem value="relocation">Relocation</MenuItem>
                                    <MenuItem value="re-sealing">Re-sealing</MenuItem>
                                    <MenuItem value="inventory">Inventory Check</MenuItem>
                                    <MenuItem value="sanitation">Sanitation</MenuItem>
                                </Select>
                            </FormControl>
                        </Paper>
                        {hasNewItems && (
                            <Alert severity="info" sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>New items detected!</Typography>
                                <Typography variant="body2">These will be added to your database upon saving:</Typography>
                                <Box component="ul" sx={{ pl: 2, mt: 0.5, fontSize: '0.875rem' }}>
                                    {newSpecies.length > 0 && <li><strong>Species:</strong> {newSpecies.join(', ')}</li>}
                                    {newVendors.length > 0 && <li><strong>Vendors:</strong> {newVendors.join(', ')}</li>}
                                    {newLocations.length > 0 && <li><strong>Locations:</strong> {newLocations.join(', ')}</li>}
                                </Box>
                            </Alert>
                        )}
                        <Stack spacing={2}>
                           {extractedEvents.map(event => {
                                const isNewProduct = event.product && newSpecies.includes(event.product);
                                const isNewSupplier = event.supplier && newVendors.includes(event.supplier);
                                const isNewLocation = event.location && newLocations.includes(event.location);

                                return (
                                <Paper key={event.tempId} variant="outlined" sx={{ p: 2, position: 'relative' }}>
                                    <IconButton
                                        onClick={() => removeEvent(event.tempId)}
                                        size="small"
                                        sx={{ position: 'absolute', top: 8, right: 8, color: 'text.secondary', '&:hover': { color: 'error.main' } }}
                                    >
                                        <DeleteIcon fontSize="small" />
                                    </IconButton>
                                    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' }, gap: 2 }}>
                                        <TextField
                                            label="Event"
                                            value={event.eventType || ''}
                                            onChange={e => handleDataChange(event.tempId, 'eventType', e.target.value)}
                                            disabled={overrideEventType !== 'auto'}
                                            size="small"
                                            fullWidth
                                        />
                                        <Box sx={{ position: 'relative' }}>
                                            <TextField
                                                label="Product"
                                                value={event.product || ''}
                                                onChange={e => handleDataChange(event.tempId, 'product', e.target.value)}
                                                size="small"
                                                fullWidth
                                            />
                                            {isNewProduct && <Chip label="New" color="success" size="small" sx={{ position: 'absolute', top: -8, right: -8 }} />}
                                        </Box>
                                        <TextField
                                            label="Quantity"
                                            type="number"
                                            value={event.quantity ?? ''}
                                            onChange={e => handleDataChange(event.tempId, 'quantity', e.target.value)}
                                            size="small"
                                            fullWidth
                                        />
                                        <Box sx={{ position: 'relative' }}>
                                            <TextField
                                                label="Supplier"
                                                value={event.supplier || ''}
                                                onChange={e => handleDataChange(event.tempId, 'supplier', e.target.value)}
                                                size="small"
                                                fullWidth
                                            />
                                            {isNewSupplier && <Chip label="New" color="success" size="small" sx={{ position: 'absolute', top: -8, right: -8 }} />}
                                        </Box>
                                        <Box sx={{ position: 'relative' }}>
                                            <TextField
                                                label="Location"
                                                value={event.location || ''}
                                                onChange={e => handleDataChange(event.tempId, 'location', e.target.value)}
                                                size="small"
                                                fullWidth
                                            />
                                            {isNewLocation && <Chip label="New" color="success" size="small" sx={{ position: 'absolute', top: -8, right: -8 }} />}
                                        </Box>
                                        <TextField
                                            label="Date"
                                            type="date"
                                            value={event.date || ''}
                                            onChange={e => handleDataChange(event.tempId, 'date', e.target.value)}
                                            size="small"
                                            fullWidth
                                            InputLabelProps={{ shrink: true }}
                                        />
                                        <TextField
                                            label="Notes"
                                            value={event.notes || ''}
                                            onChange={e => handleDataChange(event.tempId, 'notes', e.target.value)}
                                            size="small"
                                            fullWidth
                                            sx={{ gridColumn: { xs: '1', sm: 'span 2', md: 'span 3' } }}
                                        />
                                    </Box>
                                </Paper>
                                );
                           })}
                        </Stack>
                    </Box>
                );
            default: return null;
        }
    };
    
    const handleModalClose = useCallback(() => {
        setStep('upload');
        setOriginalEvents([]);
        setExtractedEvents([]);
        setError(null);
        onClose();
    }, [onClose]);
    
    return (
        <Dialog
            open
            onClose={handleModalClose}
            maxWidth="lg"
            fullWidth
            PaperProps={{ sx: { bgcolor: 'background.default' } }}
        >
            <DialogTitle
                sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', borderBottom: '1px solid', borderColor: 'divider' }}
            >
                Import from File
                <IconButton onClick={handleModalClose} edge="end" aria-label="Close import dialog">
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent sx={{ p: 0 }}>
                <Box sx={{ flexGrow: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
                    {renderContent()}
                </Box>
            </DialogContent>
            {step === 'confirm' && (
                <DialogActions sx={{ borderTop: '1px solid', borderColor: 'divider', px: 3, py: 2 }}>
                    <Button onClick={handleModalClose} color="inherit">
                        Cancel
                    </Button>
                    <Button onClick={() => onSave(extractedEvents)} variant="contained" disabled={extractedEvents.length === 0}>
                        Save {extractedEvents.length} Items
                    </Button>
                </DialogActions>
            )}
        </Dialog>
    );
};
