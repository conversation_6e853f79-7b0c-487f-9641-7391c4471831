import React, { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import Button from '@mui/material/Button';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import { addLot } from '../../firebase/lotService';
import { Lot, LotStatus } from '../../types';

interface AddLotModalProps {
  onClose: () => void;
}

export const AddLotModal: React.FC<AddLotModalProps> = ({ onClose }) => {
  const [lotId, setLotId] = useState('');
  const [species, setSpecies] = useState('');
  const [origin, setOrigin] = useState('');
  const [initialWeight, setInitialWeight] = useState('');
  const [harvestDate, setHarvestDate] = useState(new Date().toISOString().split('T')[0]);
  const [status, setStatus] = useState<LotStatus>('Available');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!lotId || !species || !origin || !initialWeight || !harvestDate) {
      setError('Please fill out all fields.');
      return;
    }

    const newLot: Omit<Lot, 'id' | 'createdAt' | 'updatedAt'> = {
      lotId,
      species,
      origin,
      initialWeight: parseFloat(initialWeight),
      harvestDate,
      status,
    };

    try {
      await addLot(newLot);
      onClose();
    } catch (err) {
      setError('Failed to create lot. Please try again.');
      console.error(err);
    }
  };

  return (
    <Dialog open onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Track New Lot</DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent dividers>
          <Box
            sx={{
              display: 'grid',
              gap: 2,
              gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, minmax(0, 1fr))' },
            }}
          >
            <TextField label="Lot ID" value={lotId} onChange={(e) => setLotId(e.target.value)} fullWidth autoFocus />
            <TextField label="Species" value={species} onChange={(e) => setSpecies(e.target.value)} fullWidth />
            <TextField label="Origin" value={origin} onChange={(e) => setOrigin(e.target.value)} fullWidth />
            <TextField
              label="Initial Weight (kg)"
              type="number"
              value={initialWeight}
              onChange={(e) => setInitialWeight(e.target.value)}
              fullWidth
            />
            <TextField
              label="Harvest Date"
              type="date"
              value={harvestDate}
              onChange={(e) => setHarvestDate(e.target.value)}
              fullWidth
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              select
              label="Status"
              value={status}
              onChange={(e) => setStatus(e.target.value as LotStatus)}
              fullWidth
            >
              <MenuItem value="Available">Available</MenuItem>
              <MenuItem value="In-Transit">In-Transit</MenuItem>
              <MenuItem value="Processed">Processed</MenuItem>
              <MenuItem value="Warning">Warning</MenuItem>
            </TextField>
          </Box>
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} color="inherit">
            Cancel
          </Button>
          <Button type="submit" variant="contained">
            Save Lot
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
