import React, { useState, useEffect } from 'react';
import {
  alpha,
  Box,
  Chip,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import IosShareIcon from '@mui/icons-material/IosShare';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import { Lot, LotMovement } from '../../types';
import { onLotMovementsUpdate } from '../../firebase/lotService';
import { MovementHistory } from './MovementHistory';
import { AddMovementModal } from './AddMovementModal';
import { SurfaceCard } from '../common/SurfaceCard';
import { getStatusChipProps } from './statusHelpers';

interface LotDetailsProps {
  lot: Lot;
  onShare?: () => void;
}

export const LotDetails: React.FC<LotDetailsProps> = ({ lot, onShare }) => {
  const [movements, setMovements] = useState<LotMovement[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (!lot.id) return;
    const unsubscribe = onLotMovementsUpdate(lot.id, setMovements);
    return () => unsubscribe();
  }, [lot.id]);

  const currentStatus = movements.length > 0 ? movements[0] : null;
  const detailItems = [
    { label: 'Species', value: lot.species },
    { label: 'Origin', value: lot.origin },
    { label: 'Initial Weight', value: `${lot.initialWeight} kg` },
    { label: 'Harvest Date', value: lot.harvestDate },
  ];

  return (
    <Stack spacing={3}>
      <SurfaceCard>
        <Stack direction="row" justifyContent="space-between" alignItems="flex-start" spacing={2}>
          <Box sx={{ display: 'grid', gap: 2 }}>
            <Typography variant="h6" fontWeight={700} color="text.primary">
              Details for Lot #{lot.lotId}
            </Typography>
            <Box
              sx={{
                display: 'grid',
                gap: 2,
                gridTemplateColumns: { xs: 'repeat(1, minmax(0, 1fr))', sm: 'repeat(2, minmax(0, 1fr))' },
                mt: 1,
              }}
            >
              {detailItems.map(({ label, value }) => (
                <Box key={label}>
                  <Typography variant="caption" color="text.secondary">
                    {label}
                  </Typography>
                  <Typography variant="body2" fontWeight={500} color="text.primary">
                    {value}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
          <IconButton aria-label="Share lot details" onClick={onShare} disabled={!onShare}>
            <IosShareIcon />
          </IconButton>
        </Stack>
      </SurfaceCard>

      {currentStatus && (
        <SurfaceCard>
          <Stack spacing={2}>
            <Typography variant="subtitle1" fontWeight={700} color="text.primary">
              Current Status
            </Typography>
            <Stack direction="row" spacing={2} alignItems="flex-start">
              <Box
                sx={(theme) => ({
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 48,
                  height: 48,
                  borderRadius: '999px',
                  bgcolor: alpha(theme.palette.primary.main, 0.12),
                  color: 'primary.main',
                  flexShrink: 0,
                })}
              >
                <WarehouseIcon />
              </Box>
              <Stack spacing={1} flex={1}>
                <Typography variant="body1" fontWeight={600} color="text.primary">
                  {currentStatus.location}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Last Update: {currentStatus.timestamp?.seconds 
                    ? new Date(currentStatus.timestamp.seconds * 1000).toLocaleString()
                    : 'Unknown'}
                </Typography>
                <Chip size="small" {...getStatusChipProps(lot.status)} />
              </Stack>
            </Stack>
          </Stack>
        </SurfaceCard>
      )}

      <MovementHistory movements={movements} onAddMovement={() => setIsModalOpen(true)} />

      {isModalOpen && (
        <AddMovementModal lotId={lot.id} onClose={() => setIsModalOpen(false)} />
      )}
    </Stack>
  );
};
