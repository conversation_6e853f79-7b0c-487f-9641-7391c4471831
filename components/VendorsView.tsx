import React, { useCallback, useEffect, useState } from 'react';
import { Box, Typography, Stack, Button, TextField, useTheme } from '@mui/material';
import { alpha } from '@mui/material/styles';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { db } from '../firebase/config';
import { Vendor } from '../types';
import { BuildingStorefrontIcon, PlusIcon, PhotoIcon } from './IconComponents';
import { uploadImage } from '../utils/imageUtils';
import { docToPlainObject } from '../utils/firestoreUtils';
import { SurfaceCard } from './common/SurfaceCard';

export const VendorsView: React.FC = () => {
  const theme = useTheme();
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);

  const [editingId, setEditingId] = useState<string | null>(null);
  const [name, setName] = useState('');
  const [contactPerson, setContactPerson] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [address, setAddress] = useState('');
  const [notes, setNotes] = useState('');
  const [productsCarried, setProductsCarried] = useState('');
  const [sourcingLeadTimeDays, setSourcingLeadTimeDays] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const revokePreviewUrl = useCallback((previewUrl: string | null) => {
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }
  }, []);

  useEffect(() => {
    try {
      const vendorsQuery = query(collection(db, 'vendors'), orderBy('name'));
      const unsubscribe = onSnapshot(
        vendorsQuery,
        (snapshot: QuerySnapshot<DocumentData>) => {
          const list: Vendor[] = snapshot.docs.map((document) => docToPlainObject<Vendor>(document));
          setVendors(list);
          setLoading(false);
        },
        (err) => {
          console.error(err);
          setError('Failed to fetch vendors.');
          setLoading(false);
        },
      );
      return () => unsubscribe();
    } catch (err) {
      console.error(err);
      setError('Failed to initialize Firebase for vendors.');
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    return () => {
      revokePreviewUrl(imagePreview);
    };
  }, [imagePreview, revokePreviewUrl]);

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      revokePreviewUrl(imagePreview);
      setImageFile(file);
      setImagePreview(URL.createObjectURL(file));
    }
  };

  const resetForm = () => {
    revokePreviewUrl(imagePreview);
    setEditingId(null);
    setName('');
    setContactPerson('');
    setPhone('');
    setEmail('');
    setAddress('');
    setNotes('');
    setProductsCarried('');
    setSourcingLeadTimeDays('');
    setImageFile(null);
    setImagePreview(null);
    setIsFormOpen(false);
  };

  const handleEdit = (vendor: Vendor) => {
    revokePreviewUrl(imagePreview);
    setEditingId(vendor.id);
    setName(vendor.name);
    setContactPerson(vendor.contactPerson || '');
    setPhone(vendor.phone || '');
    setEmail(vendor.email || '');
    setAddress(vendor.address || '');
    setNotes(vendor.notes || '');
    setProductsCarried(vendor.productsCarried?.join('\n') || '');
    setSourcingLeadTimeDays(vendor.sourcingLeadTimeDays?.toString() || '');
    setImageFile(null);
    setImagePreview(vendor.imageUrl || null);
    setIsFormOpen(true);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!name.trim()) {
      alert('Vendor name is required.');
      return;
    }

    const productsArray = productsCarried
      .split('\n')
      .map((value) => value.trim())
      .filter(Boolean);

    const vendorData: Partial<Vendor> = {
      name: name.trim(),
      contactPerson: contactPerson.trim() || undefined,
      phone: phone.trim() || undefined,
      email: email.trim() || undefined,
      address: address.trim() || undefined,
      notes: notes.trim() || undefined,
      productsCarried: productsArray,
      sourcingLeadTimeDays: sourcingLeadTimeDays ? 
        (() => {
          const parsed = parseInt(sourcingLeadTimeDays, 10);
          return !isNaN(parsed) ? parsed : undefined;
        })() : undefined,
    };

    if (imageFile) {
      try {
        const imageUrl = await uploadImage(imageFile, `vendors/${Date.now()}_${imageFile.name}`);
        vendorData.imageUrl = imageUrl;
      } catch (err) {
        console.error('Failed to upload image', err);
        alert('Failed to upload image.');
        return;
      }
    } else if (editingId && imagePreview) {
      vendorData.imageUrl = imagePreview;
    }

    try {
      if (editingId) {
        const vendorRef = doc(db, 'vendors', editingId);
        await updateDoc(vendorRef, vendorData);
      } else {
        await addDoc(collection(db, 'vendors'), vendorData);
      }
      resetForm();
    } catch (err) {
      console.error('Error saving vendor', err);
      alert('Failed to save vendor.');
    }
  };

  return (
    <SurfaceCard sx={{ p: { xs: 3, sm: 4 }, width: '100%' }}>
      <Stack
        direction={{ xs: 'column', md: 'row' }}
        justifyContent="space-between"
        alignItems={{ xs: 'flex-start', md: 'center' }}
        spacing={2}
        sx={{ pb: 2, borderBottom: 1, borderColor: 'divider', mb: 3 }}
      >
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box
            sx={{
              bgcolor: alpha(theme.palette.secondary.main, 0.1),
              color: 'secondary.main',
              p: 1,
              borderRadius: 1,
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Box component={BuildingStorefrontIcon} sx={{ width: 24, height: 24 }} />
          </Box>
          <Typography variant="h5" component="h1" sx={{ color: 'text.primary', fontWeight: 700 }}>
            Vendor Management
          </Typography>
        </Stack>
        <Button
          onClick={() => {
            resetForm();
            setIsFormOpen(true);
          }}
          variant="contained"
          startIcon={<Box component={PlusIcon} sx={{ width: 20, height: 20 }} />}
        >
          Add Vendor
        </Button>
      </Stack>

      {isFormOpen && (
        <SurfaceCard sx={{ bgcolor: 'background.surface', p: 3, mb: 3 }}>
          <Typography variant="h6" sx={{ color: 'text.primary', mb: 2, fontWeight: 600 }}>
            {editingId ? 'Edit Vendor' : 'Add New Vendor'}
          </Typography>
          <Box component="form" onSubmit={handleSubmit} sx={{ display: 'grid', gap: 2, gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' } }}>
            <TextField
              label="Vendor Name"
              id="vendor-name"
              value={name}
              onChange={(event) => setName(event.target.value)}
              required
              fullWidth
              autoFocus
            />
            <TextField
              label="Contact Person"
              id="vendor-contact"
              value={contactPerson}
              onChange={(event) => setContactPerson(event.target.value)}
              fullWidth
            />
            <TextField
              label="Phone"
              id="vendor-phone"
              type="tel"
              value={phone}
              onChange={(event) => setPhone(event.target.value)}
              fullWidth
            />
            <TextField
              label="Email"
              id="vendor-email"
              type="email"
              value={email}
              onChange={(event) => setEmail(event.target.value)}
              fullWidth
              sx={{ gridColumn: { md: 'span 2' } }}
            />
            <TextField
              label="Address"
              id="vendor-address"
              value={address}
              onChange={(event) => setAddress(event.target.value)}
              fullWidth
              sx={{ gridColumn: { md: 'span 2' } }}
            />
            <TextField
              label="Products Carried (one per line)"
              id="vendor-products"
              value={productsCarried}
              onChange={(event) => setProductsCarried(event.target.value)}
              multiline
              minRows={3}
              fullWidth
            />
            <TextField
              label="Sourcing Lead Time (Days)"
              id="vendor-lead-time"
              type="number"
              value={sourcingLeadTimeDays}
              onChange={(event) => setSourcingLeadTimeDays(event.target.value)}
              fullWidth
            />
            <TextField
              label="Notes"
              id="vendor-notes"
              value={notes}
              onChange={(event) => setNotes(event.target.value)}
              multiline
              minRows={3}
              fullWidth
              sx={{ gridColumn: { md: 'span 2' } }}
            />
            <Box sx={{ gridColumn: { md: 'span 2' } }}>
              <Typography variant="body2" sx={{ color: 'text.primary', fontWeight: 500, mb: 1 }}>
                Vendor Image
              </Typography>
              <Stack direction="row" spacing={2} alignItems="center">
                <Box sx={{ flexShrink: 0 }}>
                  {imagePreview ? (
                    <Box
                      component="img"
                      src={imagePreview}
                      alt="Vendor preview"
                      sx={{ height: 64, width: 64, borderRadius: 1, objectFit: 'cover' }}
                    />
                  ) : (
                    <Box
                      sx={{
                        height: 64,
                        width: 64,
                        bgcolor: 'action.hover',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'text.secondary',
                      }}
                    >
                      <Box component={PhotoIcon} sx={{ width: 32, height: 32 }} />
                    </Box>
                  )}
                </Box>
                <Button variant="outlined" component="label" fullWidth>
                  Choose File
                  <input type="file" onChange={handleImageSelect} accept="image/*" hidden />
                </Button>
              </Stack>
            </Box>
            <Stack direction="row" justifyContent="flex-end" spacing={2} sx={{ gridColumn: { md: 'span 2' } }}>
              <Button type="button" onClick={resetForm} variant="outlined">
                Cancel
              </Button>
              <Button type="submit" variant="contained">
                {editingId ? 'Update Vendor' : 'Save Vendor'}
              </Button>
            </Stack>
          </Box>
        </SurfaceCard>
      )}

      {loading && <Typography sx={{ color: 'text.secondary' }}>Loading vendors...</Typography>}
      {error && <Typography sx={{ color: 'error.main' }}>{error}</Typography>}
      {!loading && !error && (
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' },
            gap: 2,
          }}
        >
          {vendors.length === 0 ? (
            <Typography sx={{ textAlign: 'center', color: 'text.secondary', py: 2, gridColumn: { md: 'span 2' } }}>
              No vendors added yet.
            </Typography>
          ) : (
            vendors.map((vendor) => (
              <SurfaceCard key={vendor.id} hover sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                <Stack direction="row" spacing={2} alignItems="flex-start">
                  {vendor.imageUrl && (
                    <Box
                      component="img"
                      src={vendor.imageUrl}
                      alt={vendor.name}
                      sx={{ height: 64, width: 64, borderRadius: 1, objectFit: 'cover' }}
                    />
                  )}
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" sx={{ color: 'text.primary', fontWeight: 600 }}>
                      {vendor.name}
                    </Typography>
                    {vendor.contactPerson && (
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        {vendor.contactPerson}
                        {vendor.phone ? ` - ${vendor.phone}` : ''}
                      </Typography>
                    )}
                    {vendor.email && (
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        {vendor.email}
                      </Typography>
                    )}
                    {vendor.address && (
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        {vendor.address}
                      </Typography>
                    )}
                    {vendor.sourcingLeadTimeDays && (
                      <Typography variant="caption" sx={{ color: 'text.secondary', mt: 0.5, display: 'block' }}>
                        Lead Time:{' '}
                        <Box component="span" sx={{ fontWeight: 600 }}>
                          {vendor.sourcingLeadTimeDays} days
                        </Box>
                      </Typography>
                    )}
                  </Box>
                </Stack>
                <Box sx={{ mt: 1 }}>
                  <Typography variant="subtitle2" sx={{ color: 'text.primary', fontWeight: 600 }}>
                    Products
                  </Typography>
                  <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                    {vendor.productsCarried?.join(', ') || 'N/A'}
                  </Typography>
                </Box>
                <Box sx={{ mt: 1, pt: 1, borderTop: 1, borderColor: 'divider', display: 'flex', justifyContent: 'flex-end' }}>
                  <Button onClick={() => handleEdit(vendor)} size="small" sx={{ color: 'primary.main', fontWeight: 600 }}>
                    Edit
                  </Button>
                </Box>
              </SurfaceCard>
            ))
          )}
        </Box>
      )}
    </SurfaceCard>
  );
};
