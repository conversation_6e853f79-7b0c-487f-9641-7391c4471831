import React from 'react';
import { Paper, Box, useTheme } from '@mui/material';
import type { PaperProps } from '@mui/material';
import { alpha, SxProps, Theme } from '@mui/material/styles';

interface SurfaceCardProps extends Omit<PaperProps, 'children'> {
  children: React.ReactNode;
  padding?: number | string;
  hover?: boolean;
  selected?: boolean;
  sx?: SxProps<Theme>;
  onClick?: () => void;
}

/**
 * SurfaceCard - A theme-aware wrapper around MUI Paper
 * 
 * Provides consistent styling that automatically adapts to light/dark themes:
 * - Uses theme-aware background colors
 * - Proper hover states
 * - Consistent padding and borders
 * - Accessibility-compliant contrast ratios
 */
export const SurfaceCard: React.FC<SurfaceCardProps> = ({
  children,
  variant = 'outlined',
  elevation = 0,
  padding = 3,
  component = 'div',
  hover = false,
  selected = false,
  sx = {},
  className,
  onClick,
  onKeyDown,
  tabIndex,
  role,
  ...rest
}) => {
  const theme = useTheme();
  const baseBorderRadius =
    typeof theme.shape.borderRadius === 'number'
      ? theme.shape.borderRadius
      : parseFloat(theme.shape.borderRadius ?? '0') || 0;

  const surfaceCardSx: SxProps<Theme> = {
    // Base styling
    borderRadius: baseBorderRadius / 4, // 3px for subtle rounding
    transition: theme.transitions.create(['background-color', 'border-color', 'box-shadow'], {
      duration: theme.transitions.duration.short,
    }),
    
    // Padding
    p: padding,
    
    // Background color based on theme - use CSS variables for proper theme switching
    bgcolor: selected
      ? 'var(--mui-palette-action-selected)'
      : 'var(--mui-palette-background-paper)',
    
    // Border color
    borderColor: 'var(--mui-palette-divider)',
    
    // Hover effects
    ...(hover && {
      cursor: 'pointer',
      '&:hover': {
        bgcolor: selected
          ? 'var(--mui-palette-action-selected)'
          : 'var(--mui-palette-action-hover)',
        borderColor: 'var(--mui-palette-primary-main)',
      },
    }),
    
    // Selected state
    ...(selected && {
      borderColor: 'var(--mui-palette-primary-main)',
      bgcolor: 'var(--mui-palette-action-selected)',
    }),

    // Focus styles for accessibility
    '&:focus-visible': {
      outline: 'none',
      boxShadow: `0 0 0 3px ${alpha(theme.palette.primary.main, 0.21)}`,
    },
    
    // Merge with custom sx
    ...sx,
  };

  const handleInteractiveKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    // Prevent default for Space to avoid scrolling
    if (event.key === ' ' || event.key === 'Spacebar') {
      event.preventDefault();
    }

    if (event.key === 'Enter' || event.key === ' ' || event.key === 'Spacebar') {
      onClick?.();
    }
    onKeyDown?.(event);
  };

  const interactiveTabIndex = onClick ? tabIndex ?? 0 : tabIndex;
  const interactiveRole = onClick ? role ?? 'button' : role;
  const interactiveKeyDown = onClick ? handleInteractiveKeyDown : onKeyDown;

  return (
    <Paper
      variant={variant}
      elevation={elevation}
      component={component}
      className={className}
      onClick={onClick}
      onKeyDown={interactiveKeyDown}
      sx={surfaceCardSx}
      tabIndex={interactiveTabIndex}
      role={interactiveRole}
      {...rest}
    >
      {children}
    </Paper>
  );
};

// Specialized variants for common use cases

interface KpiCardProps extends Omit<SurfaceCardProps, 'children'> {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  subtitle?: string;
}

/**
 * KpiCard - Specialized SurfaceCard for displaying KPI metrics
 */
export const KpiCard: React.FC<KpiCardProps> = ({
  title,
  value,
  icon,
  subtitle,
  ...props
}) => {
  const theme = useTheme();

  return (
    <SurfaceCard {...props}>
      <Box display="flex" alignItems="center" gap={2}>
        {icon && (
          <Box
            sx={{
              p: 1.5,
              borderRadius: '50%',
              bgcolor: alpha(theme.palette.primary.main, 0.125), // ~12.5% opacity
              color: theme.palette.primary.main,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {icon}
          </Box>
        )}
        <Box flex={1}>
          <Box
            component="p"
            sx={{
              fontSize: '0.875rem',
              color: theme.palette.text.secondary,
              margin: 0,
              marginBottom: 0.5,
            }}
          >
            {title}
          </Box>
          <Box
            component="p"
            sx={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: theme.palette.text.primary,
              margin: 0,
              marginBottom: subtitle ? 0.25 : 0,
            }}
          >
            {value}
          </Box>
          {subtitle && (
            <Box
              component="p"
              sx={{
                fontSize: '0.75rem',
                color: theme.palette.text.secondary,
                margin: 0,
              }}
            >
              {subtitle}
            </Box>
          )}
        </Box>
      </Box>
    </SurfaceCard>
  );
};

interface ChartCardProps extends Omit<SurfaceCardProps, 'children'> {
  title: string;
  children: React.ReactNode;
}

/**
 * ChartCard - Specialized SurfaceCard for displaying charts and visualizations
 */
export const ChartCard: React.FC<ChartCardProps> = ({
  title,
  children,
  ...props
}) => {
  const theme = useTheme();

  return (
    <SurfaceCard {...props}>
      <Box
        component="h3"
        sx={{
          fontSize: '1rem',
          fontWeight: 600,
          color: theme.palette.text.primary,
          margin: 0,
          marginBottom: 2,
        }}
      >
        {title}
      </Box>
      {children}
    </SurfaceCard>
  );
};

export default SurfaceCard;
