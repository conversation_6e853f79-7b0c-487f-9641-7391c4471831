#!/bin/bash

# Deploy script for Firebase Functions
# This script sets up and deploys the secure AI processing functions

set -e

echo "🚀 Deploying Firebase Functions for HACCP Helper"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please login first:"
    echo "firebase login"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "firebase.json" ]; then
    echo "❌ firebase.json not found. Please run this script from the project root."
    exit 1
fi

# Install function dependencies
echo "📦 Installing function dependencies..."
cd functions
npm install
cd ..

# Check if Gemini API key secret exists
echo "🔑 Checking Gemini API key configuration..."
if ! firebase functions:secrets:versions:list GEMINI_API_KEY --limit 1 >/dev/null 2>&1; then
    echo "⚠️  Gemini API key secret not found."
    read -p "Do you want to configure it now? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Cannot deploy without the Gemini API key. Exiting."
        exit 1
    fi

    while true; do
        read -s -p "Enter your Gemini API key: " api_key
        echo
        read -s -p "Confirm your Gemini API key: " api_key_confirm
        echo

        if [[ -z "$api_key" ]]; then
            echo "❌ API key cannot be empty. Please try again."
            continue
        fi

        if [[ "$api_key" != "$api_key_confirm" ]]; then
            echo "❌ API keys do not match. Please try again."
            continue
        fi

        if [[ ! "$api_key" =~ ^AIzaSy[A-Za-z0-9]{35}$ ]]; then
            echo "❌ Invalid API key format. Expected a Google API key starting with 'AIzaSy' followed by 35 letters or numbers (total length 39)."
            continue
        fi

        if printf '%s' "$api_key" | firebase functions:secrets:set GEMINI_API_KEY >/dev/null; then
            unset api_key
            unset api_key_confirm
            echo "✅ Gemini API key stored securely in Firebase Secrets Manager."
            break
        else
            echo "❌ Failed to store the API key. Please ensure you have the required permissions and try again."
        fi
    done
else
    echo "✅ Gemini API key secret is already configured."
fi

# Deploy functions
echo "🚀 Deploying functions..."
firebase deploy --only functions

echo ""
echo "✅ Deployment complete!"
echo ""
echo "📋 Next steps:"

# Extract project ID dynamically
PROJECT_ID=$(firebase functions:config:get project.id 2>/dev/null || grep -oP '"projectId":\s*"\K[^"]+' ~/.config/gcloud/configurations/config_default 2>/dev/null || echo "<YOUR_PROJECT_ID>")

echo "1. Test the function at: https://us-central1-${PROJECT_ID}.cloudfunctions.net/parseSpreadsheet"
echo "2. Monitor logs in Firebase Console > Functions > Logs"
echo "3. Update any hardcoded URLs in your client code if needed"
echo ""
echo "🔒 Security features enabled:"
echo "- Server-side API key management"
echo "- Rate limiting (10 requests/minute per IP)"
echo "- Request size validation (max 1MB)"
echo "- Input sanitization and validation"
echo ""
