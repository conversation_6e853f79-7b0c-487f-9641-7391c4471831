rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    function isAuthenticated() {
      return request.auth != null;
    }

    // Returns true if the existing document is owned by the requesting user
    function isOwner() {
      return request.auth != null &&
             resource.data.keys().hasAny(['ownerId', 'userId', 'createdBy']) &&
             (
               (resource.data.ownerId != null && resource.data.ownerId == request.auth.uid) ||
               (resource.data.userId != null && resource.data.userId == request.auth.uid) ||
               (resource.data.createdBy != null && resource.data.createdBy == request.auth.uid)
             );
    }

    // For create, require the incoming document to be owned by the requester
    function isCreatingAsOwner() {
      return request.auth != null &&
             (
               (request.resource.data.ownerId != null && request.resource.data.ownerId == request.auth.uid) ||
               (request.resource.data.userId != null && request.resource.data.userId == request.auth.uid) ||
               (request.resource.data.createdBy != null && request.resource.data.createdBy == request.auth.uid)
             );
    }

    // Internal rate limiting counters are write-only via Cloud Functions.
    match /rateLimits/{document=**} {
      allow read, write: if false;
    }

    // User-specific data - users can only access their own documents
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Add additional collection-specific rules with appropriate access controls
    // Example for shared data:
    // match /shared/{docId} {
    //   allow read: if request.auth != null;
    //   allow write: if request.auth != null && <ownership_check>;
    // }

    // Shared reference data - readable/writable by authenticated users
    match /locations/{docId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAuthenticated();
    }

    match /vendors/{docId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAuthenticated();
    }

    match /species/{docId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAuthenticated();
    }

    // Domain data with ownership
    match /events/{docId} {
      allow read: if isOwner();
      allow create: if isCreatingAsOwner();
      allow update, delete: if isOwner();
    }

    match /purchaseOrders/{docId} {
      allow read: if isOwner();
      allow create: if isCreatingAsOwner();
      allow update, delete: if isOwner();
    }

    match /lots/{docId} {
      allow read: if isOwner();
      allow create: if isCreatingAsOwner();
      allow update, delete: if isOwner();
    }

    match /lotMovements/{docId} {
      allow read: if isOwner();
      allow create: if isCreatingAsOwner();
      allow update, delete: if isOwner();
    }
  }
}
