# Firebase Functions for HACCP Helper

This directory contains Firebase Cloud Functions that provide secure server-side AI processing for the HACCP Helper application.

## Functions

### parseSpreadsheet
Securely processes spreadsheet data using Google Gemini AI to extract HACCP events.

**Features:**
- Server-side API key management
- Rate limiting (10 requests per minute per IP)
- Request size validation (max 1MB)
- Row count limits (max 100 rows)
- Input validation and sanitization
- Comprehensive error handling

## Setup

1. **Install dependencies:**
   ```bash
   cd functions
   npm install
   ```

2. **Configure secret (using Firebase Secret Manager):**
   ```bash
   # Store the Gemini API key in Secret Manager
   echo "your_gemini_api_key_here" | firebase functions:secrets:set GEMINI_API_KEY
   ```
   Note: Update your function implementation to use `defineSecret('GEMINI_API_KEY')` and access the value via the Functions Secret Manager APIs, rather than `functions.config()`.

3. **Deploy functions:**
   ```bash
   firebase deploy --only functions
   ```

## Development

1. **Start local emulator:**
   ```bash
   npm run serve
   ```

2. **Update client endpoint:**
   When testing locally, update the endpoint URL in `components/ImportModal.tsx` to:
   ```
   http://localhost:5001/seafood-inventory-voice/us-central1/parseSpreadsheet
   ```

## Security Features

- **API Key Protection**: Gemini API key is stored securely in Firebase Secret Manager
- **Rate Limiting**: Prevents abuse with configurable request limits
- **Input Validation**: Validates all request parameters and data types
- **Size Limits**: Prevents large requests that could cause timeouts
- **Error Handling**: Provides appropriate error messages without exposing internal details
- **CORS**: Configured to allow requests from your domain

## Monitoring

Monitor function performance and errors in the Firebase Console:
- Functions logs: Firebase Console > Functions > Logs
- Performance: Firebase Console > Functions > Usage

## Secrets

Set these using Firebase Secret Manager:

- `GEMINI_API_KEY`: Your Google Gemini API key (set via `firebase functions:secrets:set`)

## API Endpoint

**POST** `https://us-central1-seafood-inventory-voice.cloudfunctions.net/parseSpreadsheet`

**Request Body:**
```json
{
  "spreadsheetData": [...],
  "headers": "column1, column2, ...",
  "species": ["species1", "species2", ...],
  "vendors": ["vendor1", "vendor2", ...],
  "locations": ["location1", "location2", ...]
}
```

**Response:**
```json
{
  "success": true,
  "events": [...],
  "processedRows": 50,
  "totalRows": 50
}
```
