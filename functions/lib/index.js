"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseSpreadsheet = exports.cleanupExpiredRateLimits = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
const genai_1 = require("@google/genai");
// Initialize Firebase Admin
admin.initializeApp();
const GEMINI_API_KEY = functions.params.defineSecret('GEMINI_API_KEY');
// Rate limiting configuration
const RATE_LIMIT = {
    maxRequests: 10,
    windowMs: 60 * 1000, // 1 minute window
};
const USE_IN_MEMORY_RATE_LIMIT = process.env.FUNCTIONS_EMULATOR === 'true' || process.env.NODE_ENV === 'development';
// In-memory store used only for local development and emulator
const rateLimitStore = USE_IN_MEMORY_RATE_LIMIT
    ? new Map()
    : undefined;
const firestore = admin.firestore();
const rateLimitCollection = firestore.collection('rateLimits');
const RATE_LIMIT_TTL_BUFFER_MS = RATE_LIMIT.windowMs * 5;
// Request size limits
const MAX_ROWS = 100;
const MAX_REQUEST_SIZE = 1024 * 1024; // 1MB
function checkRateLimitInMemory(clientId) {
    if (!rateLimitStore) {
        return { allowed: true };
    }
    const now = Date.now();
    const clientData = rateLimitStore.get(clientId);
    if (!clientData || now > clientData.resetTime) {
        // Clean up expired entries periodically to avoid unbounded memory in dev
        if (Math.random() < 0.01) {
            for (const [key, value] of rateLimitStore.entries()) {
                if (now > value.resetTime) {
                    rateLimitStore.delete(key);
                }
            }
        }
        rateLimitStore.set(clientId, {
            count: 1,
            resetTime: now + RATE_LIMIT.windowMs,
        });
        return { allowed: true };
    }
    if (clientData.count >= RATE_LIMIT.maxRequests) {
        const retryAfterSeconds = Math.ceil((clientData.resetTime - now) / 1000);
        return { allowed: false, retryAfter: Math.max(retryAfterSeconds, 1) };
    }
    clientData.count++;
    return { allowed: true };
}
async function checkRateLimit(clientId) {
    if (USE_IN_MEMORY_RATE_LIMIT) {
        return checkRateLimitInMemory(clientId);
    }
    const nowMs = Date.now();
    const nowTimestamp = admin.firestore.Timestamp.fromMillis(nowMs);
    const resetTimestamp = admin.firestore.Timestamp.fromMillis(nowMs + RATE_LIMIT.windowMs);
    const expiryTimestamp = admin.firestore.Timestamp.fromMillis(nowMs + RATE_LIMIT.windowMs + RATE_LIMIT_TTL_BUFFER_MS);
    const docRef = rateLimitCollection.doc(clientId);
    try {
        return await firestore.runTransaction(async (tx) => {
            var _a, _b;
            const snapshot = await tx.get(docRef);
            if (!snapshot.exists) {
                tx.set(docRef, {
                    count: 1,
                    resetAt: resetTimestamp,
                    expiresAt: expiryTimestamp,
                    updatedAt: nowTimestamp,
                });
                return { allowed: true };
            }
            const data = snapshot.data();
            const resetAtMillis = (_b = (_a = data.resetAt) === null || _a === void 0 ? void 0 : _a.toMillis()) !== null && _b !== void 0 ? _b : 0;
            if (nowMs >= resetAtMillis) {
                tx.set(docRef, {
                    count: 1,
                    resetAt: resetTimestamp,
                    expiresAt: expiryTimestamp,
                    updatedAt: nowTimestamp,
                }, { merge: true });
                return { allowed: true };
            }
            const currentCount = typeof data.count === 'number' ? data.count : 0;
            if (currentCount >= RATE_LIMIT.maxRequests) {
                const retryAfterSeconds = Math.ceil((resetAtMillis - nowMs) / 1000);
                return { allowed: false, retryAfter: Math.max(retryAfterSeconds, 1) };
            }
            tx.update(docRef, {
                count: currentCount + 1,
                updatedAt: nowTimestamp,
            });
            return { allowed: true };
        });
    }
    catch (error) {
        // Fail open but surface logs so SREs can investigate backend issues
        functions.logger.error(`Failed to enforce rate limit for client ${clientId}:`, error);
        return { allowed: true };
    }
}
function parseOptionalDefaultYear(value) {
    if (value === undefined || value === null || value === '') {
        return { valid: true, value: undefined };
    }
    const numericValue = typeof value === 'number' ? value : Number.parseInt(String(value), 10);
    if (!Number.isFinite(numericValue) || !Number.isInteger(numericValue)) {
        return { valid: false, error: 'defaultYear must be an integer value' };
    }
    if (numericValue < 1900 || numericValue > 2100) {
        return {
            valid: false,
            error: 'defaultYear must be between 1900 and 2100',
        };
    }
    return { valid: true, value: numericValue };
}
function daysInMonth(year, month) {
    return new Date(year, month, 0).getDate();
}
function formatIsoDate(year, month, day) {
    if (Number.isNaN(year) ||
        Number.isNaN(month) ||
        Number.isNaN(day) ||
        month < 1 ||
        month > 12 ||
        day < 1 ||
        day > daysInMonth(year, month)) {
        return null;
    }
    const paddedMonth = String(month).padStart(2, '0');
    const paddedDay = String(day).padStart(2, '0');
    return `${year}-${paddedMonth}-${paddedDay}`;
}
function attemptNormalizeDate(rawDate, defaultYear) {
    const trimmed = rawDate.trim();
    if (!trimmed) {
        return { ok: false, reason: 'invalidFormat' };
    }
    const explicitIso = trimmed.match(/^(\d{4})[-/](\d{1,2})[-/](\d{1,2})$/);
    if (explicitIso) {
        const [, yearStr, monthStr, dayStr] = explicitIso;
        const formatted = formatIsoDate(Number(yearStr), Number(monthStr), Number(dayStr));
        return formatted
            ? { ok: true, date: formatted }
            : { ok: false, reason: 'invalidFormat' };
    }
    if (/^\d{1,2}[-/]\d{1,2}$/.test(trimmed)) {
        if (!defaultYear) {
            return { ok: false, reason: 'missingYear' };
        }
        const [monthStr, dayStr] = trimmed.split(/[-/]/);
        const formatted = formatIsoDate(defaultYear, Number(monthStr), Number(dayStr));
        return formatted
            ? { ok: true, date: formatted }
            : { ok: false, reason: 'invalidFormat' };
    }
    const hasYear = /\b\d{4}\b/.test(trimmed);
    if (!hasYear) {
        if (!defaultYear) {
            return { ok: false, reason: 'missingYear' };
        }
        const candidate = new Date(`${trimmed} ${defaultYear}`);
        if (!Number.isNaN(candidate.getTime())) {
            return {
                ok: true,
                date: candidate.toISOString().slice(0, 10),
            };
        }
        return { ok: false, reason: 'invalidFormat' };
    }
    const parsed = new Date(trimmed);
    if (!Number.isNaN(parsed.getTime())) {
        return {
            ok: true,
            date: parsed.toISOString().slice(0, 10),
        };
    }
    return { ok: false, reason: 'invalidFormat' };
}
function normalizeEventDates(events, defaultYear) {
    const warnings = [];
    const normalized = events.map(event => {
        if (!event.date) {
            return event;
        }
        const result = attemptNormalizeDate(event.date, defaultYear);
        if (result.ok) {
            return Object.assign(Object.assign({}, event), { date: result.date });
        }
        const baseWarning = `Unable to normalize date "${event.date}"`;
        if (result.reason === 'missingYear') {
            warnings.push(`${baseWarning} because no defaultYear was provided. Please supply one to disambiguate month/day values.`);
        }
        else {
            warnings.push(`${baseWarning}; unsupported format.`);
        }
        const { date: _removedDate } = event, rest = __rest(event, ["date"]);
        return rest;
    });
    return { events: normalized, warnings };
}
const AMBIGUOUS_DATE_PATTERNS = [
    /^\d{1,2}[-/]\d{1,2}$/,
    /^[A-Za-z]{3,9}\s+\d{1,2}$/,
];
function collectAmbiguousDateWarnings(data) {
    const warnings = new Set();
    const processValue = (value) => {
        if (typeof value !== 'string') {
            return;
        }
        const trimmed = value.trim();
        if (!trimmed) {
            return;
        }
        if (AMBIGUOUS_DATE_PATTERNS.some(pattern => pattern.test(trimmed))) {
            warnings.add(`Ambiguous date value "${trimmed}" detected. Provide defaultYear to ensure correct parsing.`);
        }
    };
    for (const row of data) {
        if (warnings.size >= 5) {
            break;
        }
        if (Array.isArray(row)) {
            row.forEach(processValue);
            continue;
        }
        if (row && typeof row === 'object') {
            Object.values(row).forEach(processValue);
            continue;
        }
        processValue(row);
    }
    return Array.from(warnings);
}
// Input validation
function validateRequest(data) {
    if (!data || typeof data !== 'object') {
        return { isValid: false, error: 'Invalid request body' };
    }
    const { spreadsheetData, headers, species, vendors, locations, defaultYear } = data;
    if (!Array.isArray(spreadsheetData)) {
        return { isValid: false, error: 'spreadsheetData must be an array' };
    }
    if (spreadsheetData.length === 0) {
        return { isValid: false, error: 'spreadsheetData cannot be empty' };
    }
    if (spreadsheetData.length > MAX_ROWS) {
        return {
            isValid: false,
            error: `Too many rows. Maximum allowed: ${MAX_ROWS}, received: ${spreadsheetData.length}`
        };
    }
    if (typeof headers !== 'string') {
        return { isValid: false, error: 'headers must be a string' };
    }
    if (!Array.isArray(species) || !Array.isArray(vendors) || !Array.isArray(locations)) {
        return { isValid: false, error: 'species, vendors, and locations must be arrays' };
    }
    const defaultYearResult = parseOptionalDefaultYear(defaultYear);
    if (!defaultYearResult.valid) {
        return { isValid: false, error: defaultYearResult.error };
    }
    return { isValid: true, defaultYear: defaultYearResult.value };
}
exports.cleanupExpiredRateLimits = functions.pubsub.schedule('0 0 * * *').onRun(async (context) => {
    const now = admin.firestore.Timestamp.now();
    let query = rateLimitCollection
        .where('expiresAt', '<', now)
        .orderBy('expiresAt')
        .limit(500);
    let hasMore = true;
    functions.logger.info('Starting cleanup of expired rate limit documents');
    while (hasMore) {
        const snapshot = await query.get();
        if (snapshot.empty) {
            hasMore = false;
            break;
        }
        const batch = firestore.batch();
        snapshot.docs.forEach((doc) => {
            batch.delete(doc.ref);
        });
        await batch.commit();
        functions.logger.info(`Deleted ${snapshot.docs.length} expired rate limit documents`);
        // Set up next query with cursor for pagination
        const lastDoc = snapshot.docs[snapshot.docs.length - 1];
        query = rateLimitCollection
            .where('expiresAt', '<', now)
            .orderBy('expiresAt')
            .startAfter(lastDoc)
            .limit(500);
    }
    functions.logger.info('Completed cleanup of expired rate limit documents');
});
exports.parseSpreadsheet = functions
    .runWith({ secrets: [GEMINI_API_KEY] })
    .https.onRequest(async (req, res) => {
    var _a, _b;
    // Set CORS headers
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'POST');
    res.set('Access-Control-Allow-Headers', 'Content-Type');
    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }
    try {
        // Only allow POST requests
        if (req.method !== 'POST') {
            res.status(405).json({ error: 'Method not allowed' });
            return;
        }
        // Check request size
        const contentLength = parseInt(req.get('content-length') || '0');
        if (contentLength > MAX_REQUEST_SIZE) {
            res.status(413).json({ error: 'Request too large' });
            return;
        }
        // Get client identifier for rate limiting (handle proxy/load balancer scenarios)
        const forwardedFor = req.get('x-forwarded-for');
        const clientId = forwardedFor
            ? forwardedFor.split(',')[0].trim()
            : req.ip || 'unknown';
        // Check rate limit
        const rateLimitResult = await checkRateLimit(clientId);
        if (!rateLimitResult.allowed) {
            res.status(429).json({
                error: 'Rate limit exceeded. Please try again later.',
                retryAfter: (_a = rateLimitResult.retryAfter) !== null && _a !== void 0 ? _a : Math.ceil(RATE_LIMIT.windowMs / 1000),
            });
            return;
        }
        // Validate request
        const validation = validateRequest(req.body);
        if (!validation.isValid) {
            res.status(400).json({ error: validation.error });
            return;
        }
        const { spreadsheetData, headers, species, vendors, locations, } = req.body;
        const defaultYear = validation.defaultYear;
        // Get Gemini API key from secret manager or legacy config
        const geminiApiKey = GEMINI_API_KEY.value() ||
            process.env.GEMINI_API_KEY ||
            ((_b = functions.config().gemini) === null || _b === void 0 ? void 0 : _b.api_key);
        if (!geminiApiKey) {
            functions.logger.error('GEMINI_API_KEY not configured');
            res.status(500).json({ error: 'AI service not configured' });
            return;
        }
        // Initialize Gemini AI
        const ai = new genai_1.GoogleGenAI({ apiKey: geminiApiKey });
        // Prepare data for AI processing
        const limitedData = spreadsheetData.slice(0, MAX_ROWS);
        const fullDataString = limitedData.map(row => JSON.stringify(row)).join('\n');
        functions.logger.info(`Processing ${limitedData.length} rows for client ${clientId}`);
        // Call Gemini API
        const response = await ai.models.generateContent({
            model: 'gemini-2.5-flash',
            contents: {
                parts: [{
                        text: `
              Analyze the following data extracted from a user's spreadsheet and convert it into a JSON array of HACCP events.

              File Headers: ${headers}
              Default Year: ${defaultYear !== null && defaultYear !== void 0 ? defaultYear : 'Not provided'}
              Spreadsheet Data (${limitedData.length} rows):
              ${fullDataString}

              Existing database values for matching:
              - Species: ${species.join(', ')}
              - Vendors: ${vendors.join(', ')}
              - Locations: ${locations.join(', ')}

              Your task is to map the spreadsheet columns to the fields in the provided JSON schema.
              - Infer the 'eventType' from the data (e.g., if there's a supplier, it's likely 'receiving'). Default to 'inventory' if unsure.
              - If a date is present, emit it in YYYY-MM-DD format. When the source value lacks a year and a default year is provided, apply that year before formatting. If no default year is available for a day/month value, omit the date and note the ambiguity in the record's notes field.
              - Ensure 'quantity' and 'temperature' are numbers.
              - Be precise. If a value for a field isn't in the data, omit that key from the object.
              - Your output MUST be ONLY the JSON array.
            `
                    }]
            },
            config: {
                responseMimeType: 'application/json',
                responseSchema: {
                    type: genai_1.Type.ARRAY,
                    items: {
                        type: genai_1.Type.OBJECT,
                        properties: {
                            eventType: {
                                type: genai_1.Type.STRING,
                                enum: ['receiving', 'inventory', 'disposal', 'sales', 'sanitation', 'relocation', 're-sealing']
                            },
                            product: { type: genai_1.Type.STRING, description: 'The name of the seafood species.' },
                            productForm: { type: genai_1.Type.STRING, description: 'The form of the product, e.g., Fillet, Whole.' },
                            quantity: { type: genai_1.Type.NUMBER, description: 'The quantity of the product.' },
                            unit: { type: genai_1.Type.STRING, description: 'The unit of measurement, e.g., lbs.' },
                            supplier: { type: genai_1.Type.STRING, description: 'The name of the vendor or supplier.' },
                            location: { type: genai_1.Type.STRING, description: 'The storage location.' },
                            date: { type: genai_1.Type.STRING, description: 'The date of the event in YYYY-MM-DD format.' },
                            batchNumber: { type: genai_1.Type.STRING, description: 'The batch or lot number.' },
                            temperature: { type: genai_1.Type.NUMBER, description: 'The temperature in Fahrenheit.' },
                            notes: { type: genai_1.Type.STRING, description: 'Any other relevant notes from the document.' },
                        },
                    },
                },
            },
        });
        // Parse and validate response
        let parsedEvents;
        try {
            if (!response.text) {
                throw new Error('AI response text is empty or undefined');
            }
            parsedEvents = JSON.parse(response.text);
            if (!Array.isArray(parsedEvents)) {
                throw new Error('Response is not an array');
            }
        }
        catch (parseError) {
            functions.logger.error('Failed to parse AI response:', parseError);
            res.status(500).json({ error: 'Failed to parse AI response' });
            return;
        }
        functions.logger.info(`Successfully processed ${parsedEvents.length} events`);
        const { events: normalizedEvents, warnings: normalizationWarnings } = normalizeEventDates(parsedEvents, defaultYear);
        const ambiguousWarnings = defaultYear === undefined ? collectAmbiguousDateWarnings(spreadsheetData) : [];
        const warnings = [...normalizationWarnings, ...ambiguousWarnings];
        // Return successful response
        res.status(200).json({
            success: true,
            events: normalizedEvents,
            processedRows: limitedData.length,
            totalRows: spreadsheetData.length,
            defaultYearApplied: defaultYear,
            warnings,
        });
    }
    catch (error) {
        functions.logger.error('Error in parseSpreadsheet:', error);
        // Handle specific error types
        if (error instanceof Error) {
            if (error.message.includes('does not have permission') || error.message.includes('not found')) {
                res.status(500).json({ error: 'AI service authentication failed' });
                return;
            }
            if (error.message.includes('quota') || error.message.includes('limit')) {
                res.status(429).json({ error: 'AI service quota exceeded. Please try again later.' });
                return;
            }
        }
        res.status(500).json({ error: 'Internal server error' });
    }
});
//# sourceMappingURL=index.js.map