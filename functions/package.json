{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@google/genai": "^1.27.0", "cors": "^2.8.5", "express": "^4.19.2", "firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "typescript": "^4.9.0"}, "pnpm": {"overrides": {"@types/express-serve-static-core": "^4.19.7", "@types/serve-static": "^1.15.10"}}, "private": true}