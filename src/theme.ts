import { createTheme } from '@mui/material/styles';
import {
  eventTypePalette,
  semanticSurfaces,
  textContrasts,
  borderColors,
  type EventType,
  type SurfaceType,
  type TextType,
  type BorderType
} from './theme/tokens';

// Module augmentation to extend MUI theme types
declare module '@mui/material/styles' {
  interface TypeBackground {
    surface?: string;
    surfaceSecondary?: string;
    surfaceTertiary?: string;
    hover?: string;
    selected?: string;
  }

  interface TypeText {
    tertiary?: string;
  }

  interface Palette {
    eventTypes?: Record<string, { main: string; light: string; dark: string; contrastText: string }>;
    semantic?: {
      success: string;
      warning: string;
      error: string;
      info: string;
    };
  }

  interface PaletteOptions {
    eventTypes?: Record<string, { main: string; light: string; dark: string; contrastText: string }>;
    semantic?: {
      success: string;
      warning: string;
      error: string;
      info: string;
    };
  }
}

const berry = {
  primary:   { 50:'#eef2ff',100:'#e0e7ff',200:'#c7d2fe',300:'#a5b4fc',400:'#818cf8',500:'#6366f1',600:'#5458d9',700:'#4648b8',800:'#373996',900:'#2b2f7a' },
  secondary: { 50:'#f2fbff',100:'#def5ff',200:'#bfeaff',300:'#94dbff',400:'#66c7ff',500:'#38b6ff',600:'#2096db',700:'#1c79b2',800:'#195f8f',900:'#154c73' },
  neutral:   { 0:'#ffffff',50:'#f7f7fb',100:'#eef0f4',200:'#e5e7eb',300:'#d3d6de',600:'#556070',800:'#1f2430',900:'#0f131b' },
};

export const theme = createTheme({
  cssVariables: { colorSchemeSelector: 'class' },
  colorSchemes: {
    light: {
      palette: {
        primary: { main: berry.primary[500] },
        secondary: { main: berry.secondary[500] },
        background: {
          default: berry.neutral[50],
          paper: berry.neutral[0],
          // Add semantic surfaces
          surface: semanticSurfaces.light.primary,
          surfaceSecondary: semanticSurfaces.light.secondary,
          surfaceTertiary: semanticSurfaces.light.tertiary,
          hover: semanticSurfaces.light.hover,
          selected: semanticSurfaces.light.selected,
        },
        text: {
          primary: berry.neutral[900],
          secondary: '#6b7280',
          // Add semantic text colors
          tertiary: textContrasts.light.tertiary,
          disabled: textContrasts.light.disabled,
        },
        divider: borderColors.light.default,
        // Add event type colors
        eventTypes: eventTypePalette.light,
        // Add semantic colors
        semantic: {
          success: semanticSurfaces.light.success,
          warning: semanticSurfaces.light.warning,
          error: semanticSurfaces.light.error,
          info: semanticSurfaces.light.info,
        },
      },
    },
    dark: {
      palette: {
        primary: { main: berry.primary[400] },
        secondary: { main: berry.secondary[400] },
        background: {
          default: '#0b0f16',
          paper: '#0f131b',
          // Add semantic surfaces
          surface: semanticSurfaces.dark.primary,
          surfaceSecondary: semanticSurfaces.dark.secondary,
          surfaceTertiary: semanticSurfaces.dark.tertiary,
          hover: semanticSurfaces.dark.hover,
          selected: semanticSurfaces.dark.selected,
        },
        text: {
          primary: '#e5e7eb',
          secondary: '#98a2b3',
          // Add semantic text colors
          tertiary: textContrasts.dark.tertiary,
          disabled: textContrasts.dark.disabled,
        },
        divider: borderColors.dark.default,
        // Add event type colors
        eventTypes: eventTypePalette.dark,
        // Add semantic colors
        semantic: {
          success: semanticSurfaces.dark.success,
          warning: semanticSurfaces.dark.warning,
          error: semanticSurfaces.dark.error,
          info: semanticSurfaces.dark.info,
        },
      },
    },
  },
  shape: { borderRadius: 6 }, // Reduced from 12 to 6 for better content visibility
  components: {
    MuiPaper: {
      defaultProps: { variant: 'outlined', elevation: 0 },
      styleOverrides: { root: { borderColor: 'var(--mui-palette-divider)' } },
    },
    MuiAppBar: { styleOverrides: { root: { boxShadow: 'none', borderBottom: '1px solid var(--mui-palette-divider)' } } },
    MuiDrawer: { styleOverrides: { paper: { borderRight: '1px solid var(--mui-palette-divider)' } } },
    MuiDialog: {
      defaultProps: {
        fullWidth: true, maxWidth: 'sm',
        slotProps: {
          backdrop: { sx: { backdropFilter: 'blur(6px)', backgroundColor: 'rgba(0,0,0,.45)' } },
          paper: { sx: { borderRadius: 6 } }, // Reduced from 12 to 6 for better content visibility
        },
      },
    },
    // Add specific styling for Menu and Popover components
    MuiMenu: {
      styleOverrides: {
        paper: {
          borderRadius: 4, // Reduced border radius for dropdown menus
        },
      },
    },
    MuiPopover: {
      styleOverrides: {
        paper: {
          borderRadius: 4, // Reduced border radius for popovers
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          borderRadius: 4, // Reduced border radius for tooltips
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: (theme) => ({
        '*:focus-visible': { outline: 'none', boxShadow: '0 0 0 3px rgba(99,102,241,.35)' },
        body: {
          backgroundColor: 'var(--mui-palette-background-default)',
          color: 'var(--mui-palette-text-primary)',
        },
        html: {
          backgroundColor: 'var(--mui-palette-background-default)',
        },
      }),
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 4,
        },
      },
    },
    MuiSnackbarContent: {
      styleOverrides: {
        root: {
          borderRadius: 4,
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 4,
        },
      },
    },
  },
});
