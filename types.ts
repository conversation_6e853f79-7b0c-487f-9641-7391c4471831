export type EventType = 'receiving' | 'sales' | 'disposal' | 're-sealing' | 'sanitation' | 'thermometer-calibration' | 'inventory' | 'employee-training' | 'relocation';

export interface HACCPEvent {
  id: string;
  eventType: EventType;
  date: string;
  time: string;
  batchNumber?: string;
  product?: string;
  productForm?: string; // e.g., Fillet, Whole, Portion
  quantity?: number;
  unit?: 'lbs';
  temperature?: number;
  supplier?: string;
  location?: string; // For receiving/inventory, this is the storage location. For relocation, this is the 'to' location.
  fromLocation?: string; // For relocation events, the 'from' location.
  notes?: string;
  // Fields for receiving events
  unitPrice?: number;
  origin?: string;
  // Fields for sanitation events
  areaCleaned?: string;
  sanitizerUsed?: string;
  // Fields for calibration events
  thermometerId?: string;
  calibrationMethod?: string;
  result?: 'pass' | 'fail';
  // Fields for employee training events
  employeeName?: string;
  trainingTopic?: string;
  // Generic field
  correctiveAction?: string;
  // Image fields
  imageUrl?: string;
  imageDescription?: string;
  createdBy: string;
  createdAt?: string; 
  updatedAt?: string;
  updatedBy?: string;
}

export type UpdateEventField = <K extends keyof HACCPEvent>(
  eventId: string,
  field: K,
  value: HACCPEvent[K]
) => Promise<boolean>;

export interface Vendor {
  id: string;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  address?: string;
  notes?: string;
  imageUrl?: string;
  productsCarried?: string[];
  sourcingLeadTimeDays?: number;
}

export interface Species {
  id:string;
  name: string;
  category?: 'Fish' | 'Shellfish' | 'Other';
  productForms: string[];
  qualityControlNotes: string;
  imageUrl?: string;
}

export interface Location {
  id: string;
  name: string;
  description?: string;
}

export interface PurchaseOrder {
    id: string;
    vendorId: string;
    vendorName: string;
    species: string;
    quantity: number;
    unit: 'lbs';
    expectedDeliveryDate: string;
    status: 'planning' | 'ordered' | 'received';
    createdAt: string;
}


export interface ConversationTurn {
  id: string;
  speaker: 'user' | 'model';
  text: string;
}

export type EmailContext = 
    | { type: 'order', order: PurchaseOrder, vendor: Vendor }
    | { type: 'receiving', event: HACCPEvent };

export interface TempStickSensor {
  sensor_id: string;
  sensor_name: string;
  last_temp: number;
  last_humidity: number;
  last_checkin: string; // Renamed from last_update
  battery_pct: number;  // Renamed from last_battery
  rssi: number;         // Renamed from last_signal
  temp_f_c?: 'f' | 'c'; // Kept as optional, from list view
  alert_triggered?: boolean; // Kept as optional
  offline?: string;
}

export interface TempStickReading {
  sensor_time: string; // ISO 8601 date string
  temperature: number;
  humidity: number;
}

export interface TempStickReadingsResponse {
  type: 'success';
  message: 'get messages';
  data: {
    start: string;
    end: string;
    readings: TempStickReading[];
  };
}

export type LotStatus = 'Available' | 'In-Transit' | 'Processed' | 'Warning';

export interface Lot {
  id: string; // Firestore document ID
  lotId: string; // Human-readable ID, e.g., "SF-2024-8421"
  species: string;
  origin: string;
  initialWeight: number;
  harvestDate: string; // YYYY-MM-DD
  status: LotStatus;
  createdAt?: any; // Firestore ServerTimestamp
  updatedAt?: any; // Firestore ServerTimestamp
}

export type LotMovementType = 'Arrival' | 'Departure' | 'Processing' | 'Other';

export interface LotMovement {
  id: string; // Firestore document ID
  lotId: string; // The ID of the lot this movement belongs to
  location: string;
  timestamp: any; // Firestore ServerTimestamp
  type: LotMovementType;
  description: string;
}
